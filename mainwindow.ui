<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>700</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>简易邮件发送器</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout">
    
    <!-- 发件人信息区域 -->
    <item>
     <widget class="QGroupBox" name="groupSender">
      <property name="title">
       <string>发件人信息</string>
      </property>
      <layout class="QGridLayout" name="senderLayout">
       <item row="0" column="0">
        <widget class="QLabel" name="labelSenderEmail">
         <property name="text">
          <string>发件邮箱:</string>
         </property>
         <property name="minimumWidth">
          <number>80</number>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QLineEdit" name="edtSenderEmail">
         <property name="placeholderText">
          <string><EMAIL></string>
         </property>
        </widget>
       </item>
       <item row="0" column="2">
        <widget class="QLabel" name="labelSenderName">
         <property name="text">
          <string>发件人名称:</string>
         </property>
         <property name="minimumWidth">
          <number>80</number>
         </property>
        </widget>
       </item>
       <item row="0" column="3">
        <widget class="QLineEdit" name="edtSenderName">
         <property name="placeholderText">
          <string>张三</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
    
    <!-- 收件人信息区域 -->
    <item>
     <widget class="QGroupBox" name="groupRecipients">
      <property name="title">
       <string>收件人信息</string>
      </property>
      <layout class="QVBoxLayout" name="recipientsLayout">
       <item>
        <layout class="QHBoxLayout" name="recipientControlLayout">
         <item>
          <widget class="QLineEdit" name="edtNewRecipient">
           <property name="placeholderText">
            <string>输入收件人邮箱地址，按回车添加</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="btnAddRecipient">
           <property name="text">
            <string>添加</string>
           </property>
           <property name="maximumWidth">
            <number>60</number>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="btnRemoveRecipient">
           <property name="text">
            <string>删除选中</string>
           </property>
           <property name="maximumWidth">
            <number>80</number>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QListWidget" name="listRecipients">
         <property name="selectionMode">
          <enum>QAbstractItemView::MultiSelection</enum>
         </property>
         <property name="maximumHeight">
          <number>100</number>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
    
    <!-- 邮件内容区域 -->
    <item>
     <widget class="QGroupBox" name="groupContent">
      <property name="title">
       <string>邮件内容</string>
      </property>
      <layout class="QVBoxLayout" name="contentLayout">
       <item>
        <layout class="QHBoxLayout" name="subjectLayout">
         <item>
          <widget class="QLabel" name="labelSubject">
           <property name="text">
            <string>邮件主题:</string>
           </property>
           <property name="minimumWidth">
            <number>80</number>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="edtSubject">
           <property name="placeholderText">
            <string>请输入邮件主题</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QLabel" name="labelBody">
         <property name="text">
          <string>邮件正文:</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPlainTextEdit" name="txtEmailBody">
         <property name="placeholderText">
          <string>请输入邮件正文内容...</string>
         </property>
         <property name="minimumHeight">
          <number>150</number>
         </property>
        </widget>
       </item>
       <item>
        <layout class="QHBoxLayout" name="buttonLayout">
         <item>
          <spacer name="buttonSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QPushButton" name="btnSend">
           <property name="text">
            <string>发送邮件</string>
           </property>
           <property name="minimumHeight">
            <number>35</number>
           </property>
           <property name="minimumWidth">
            <number>100</number>
           </property>
           <property name="styleSheet">
            <string>QPushButton {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: bold;
    font-size: 14px;
}
QPushButton:hover {
    background-color: #45a049;
}
QPushButton:pressed {
    background-color: #3d8b40;
}
QPushButton:disabled {
    background-color: #cccccc;
    color: #666666;
}</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
    </item>
    
    <!-- 发送日志区域 -->
    <item>
     <widget class="QGroupBox" name="groupLog">
      <property name="title">
       <string>发送日志</string>
      </property>
      <layout class="QVBoxLayout" name="logLayout">
       <item>
        <layout class="QHBoxLayout" name="logControlLayout">
         <item>
          <widget class="QPushButton" name="btnClearLog">
           <property name="text">
            <string>清空日志</string>
           </property>
           <property name="maximumWidth">
            <number>80</number>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="logSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QPlainTextEdit" name="txtLog">
         <property name="readOnly">
          <bool>true</bool>
         </property>
         <property name="maximumHeight">
          <number>150</number>
         </property>
         <property name="font">
          <font>
           <family>Consolas</family>
           <pointsize>9</pointsize>
          </font>
         </property>
         <property name="styleSheet">
          <string>QPlainTextEdit {
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 4px;
}</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>800</width>
     <height>22</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>