<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>900</width>
    <height>700</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>邮件发送测试 - 每日待办</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout">
    <item>
     <layout class="QFormLayout" name="formLayout">
      <item row="0" column="0">
       <widget class="QLabel" name="labelHost">
        <property name="text">
         <string>SMTP服务器</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QLineEdit" name="edtHost"/>
      </item>
      <item row="0" column="2">
       <widget class="QLabel" name="labelPort">
        <property name="text">
         <string>端口</string>
        </property>
       </widget>
      </item>
      <item row="0" column="3">
       <widget class="QSpinBox" name="spnPort">
        <property name="maximum">
         <number>65535</number>
        </property>
        <property name="value">
         <number>465</number>
        </property>
       </widget>
      </item>
      <item row="0" column="4">
       <widget class="QCheckBox" name="chkSSL">
        <property name="text">
         <string>SSL</string>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
       </widget>
      </item>

      <item row="1" column="0">
       <widget class="QLabel" name="labelUser">
        <property name="text">
         <string>用户名</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1" colspan="4">
       <widget class="QLineEdit" name="edtUser"/>
      </item>

      <item row="2" column="0">
       <widget class="QLabel" name="labelAuth">
        <property name="text">
         <string>授权码</string>
        </property>
       </widget>
      </item>
      <item row="2" column="1" colspan="3">
       <widget class="QLineEdit" name="edtAuth">
        <property name="echoMode">
         <enum>QLineEdit::Password</enum>
        </property>
       </widget>
      </item>
      <item row="2" column="4">
       <widget class="QCheckBox" name="chkShowAuth">
        <property name="text">
         <string>显示</string>
        </property>
       </widget>
      </item>

      <item row="3" column="0">
       <widget class="QLabel" name="labelFrom">
        <property name="text">
         <string>From</string>
        </property>
       </widget>
      </item>
      <item row="3" column="1" colspan="2">
       <widget class="QLineEdit" name="edtFrom"/>
      </item>
      <item row="3" column="3">
       <widget class="QLabel" name="labelFromName">
        <property name="text">
         <string>发件人名</string>
        </property>
       </widget>
      </item>
      <item row="3" column="4">
       <widget class="QLineEdit" name="edtFromName"/>
      </item>

      <item row="4" column="0">
       <widget class="QLabel" name="labelTo">
        <property name="text">
         <string>收件人</string>
        </property>
       </widget>
      </item>
      <item row="4" column="1" colspan="4">
       <widget class="QLineEdit" name="edtTo"/>
      </item>

      <item row="5" column="0">
       <widget class="QLabel" name="labelSubject">
        <property name="text">
         <string>主题</string>
        </property>
       </widget>
      </item>
      <item row="5" column="1" colspan="3">
       <widget class="QLineEdit" name="edtSubject"/>
      </item>
      <item row="5" column="4">
       <widget class="QCheckBox" name="chkHtml">
        <property name="text">
         <string>HTML</string>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
       </widget>
      </item>

      <item row="6" column="0">
       <widget class="QLabel" name="labelBody">
        <property name="text">
         <string>正文</string>
        </property>
       </widget>
      </item>
      <item row="6" column="1" colspan="4">
       <widget class="QPlainTextEdit" name="txtBody"/>
      </item>
     </layout>
    </item>

    <item>
     <layout class="QHBoxLayout" name="buttonLayout">
      <item>
       <widget class="QPushButton" name="btnSend">
        <property name="text">
         <string>发送</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="btnClearLog">
        <property name="text">
         <string>清空日志</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
        </size>
        </property>
       </spacer>
      </item>
     </layout>
    </item>

    <item>
     <widget class="QPlainTextEdit" name="txtLog">
      <property name="readOnly">
       <bool>true</bool>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar"/>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>
