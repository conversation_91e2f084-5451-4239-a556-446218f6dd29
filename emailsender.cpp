#include "emailsender.h"
#include <QtNetwork/QSslSocket>
#include <QtNetwork/QSslConfiguration>

EmailSender::EmailSender(const SmtpConfig& cfg)
    : cfg_(cfg) {}

QString EmailSender::encodeHeaderUtf8(const QString& s) {
    return QStringLiteral("=?UTF-8?B?%1?=").arg(QString::fromLatin1(s.toUtf8().toBase64()));
}

bool EmailSender::writeCmd(QIODevice& sock, const QByteArray& cmd) {
    QByteArray data = cmd;
    if (!data.endsWith("\r\n")) data += "\r\n";
    return sock.write(data) == data.size() && sock.waitForBytesWritten(5000);
}

bool EmailSender::readReply(QIODevice& sock, int& code, QString& lines, int timeoutMs) {
    lines.clear();
    if (!sock.waitForReadyRead(timeoutMs)) return false;
    while (sock.bytesAvailable() || sock.waitForReadyRead(50)) {
        QByteArray line = sock.readLine();
        if (line.isEmpty()) break;
        lines += QString::fromLatin1(line);
        if (line.size() >= 4 && line[3] == ' ') break;
    }
    if (lines.size() < 3) return false;
    code = lines.leftRef(3).toInt();
    return true;
}

bool EmailSender::send(const QStringList& to,
                       const QString& subject,
                       const QString& body,
                       bool isHtml,
                       QString* serverLog,
                       QString* err) {
    QString log;
    auto logf = [&](const QString& s){ log += s + "\n"; };

    if (to.isEmpty()) { if(err) *err = "收件人为空"; return false; }

    QSslSocket sock;
    sock.connectToHostEncrypted(cfg_.host, cfg_.port);
    if (!sock.waitForEncrypted(15000)) {
        if (err) *err = "SSL握手失败: " + sock.errorString();
        if (serverLog) *serverLog = log;
        return false;
    }
    logf("SSL 握手成功: " + sock.sessionCipher().name());

    int code; QString reply;
    if (!readReply(sock, code, reply) || code != 220) { if (err) *err = reply; if (serverLog) *serverLog = log; return false; }
    logf(reply.trimmed());

    if (!writeCmd(sock, "EHLO localhost")) { if (err) *err = "EHLO失败"; return false; }
    if (!readReply(sock, code, reply) || code != 250) { if (err) *err = reply; if (serverLog) *serverLog = log; return false; }
    logf(reply.trimmed());

    if (!writeCmd(sock, "AUTH LOGIN")) { if (err) *err = "AUTH失败"; return false; }
    if (!readReply(sock, code, reply) || code != 334) { if (err) *err = reply; return false; }
    if (!writeCmd(sock, cfg_.username.toUtf8().toBase64())) { if (err) *err = "用户名发送失败"; return false; }
    if (!readReply(sock, code, reply) || code != 334) { if (err) *err = reply; return false; }
    if (!writeCmd(sock, cfg_.authCode.toUtf8().toBase64())) { if (err) *err = "授权码发送失败"; return false; }
    if (!readReply(sock, code, reply) || code != 235) { if (err) *err = "认证失败: " + reply; if (serverLog) *serverLog = log; return false; }
    logf("认证成功");

    if (!writeCmd(sock, "MAIL FROM:<" + cfg_.from.toUtf8() + ">")) { if (err) *err = "MAIL FROM失败"; return false; }
    if (!readReply(sock, code, reply) || code != 250) { if (err) *err = reply; return false; }

    for (const auto &rcpt : to) {
        if (!writeCmd(sock, "RCPT TO:<" + rcpt.toUtf8() + ">")) { if (err) *err = "RCPT TO失败"; return false; }
        if (!readReply(sock, code, reply) || (code != 250 && code != 251)) { if (err) *err = reply; return false; }
    }

    if (!writeCmd(sock, "DATA")) { if (err) *err = "DATA失败"; return false; }
    if (!readReply(sock, code, reply) || code != 354) { if (err) *err = reply; return false; }

    QString headers;
    QString fromName = cfg_.fromName.trimmed().isEmpty() ? cfg_.from : encodeHeaderUtf8(cfg_.fromName);
    headers += "From: " + (cfg_.fromName.isEmpty() ? QString("<%1>").arg(cfg_.from) : QString("%1 <%2>").arg(fromName, cfg_.from)) + "\r\n";
    headers += "To: " + to.join(", ") + "\r\n";
    headers += "Subject: " + encodeHeaderUtf8(subject) + "\r\n";
    headers += "MIME-Version: 1.0\r\n";
    headers += QString("Content-Type: %1; charset=UTF-8\r\n").arg(isHtml ? "text/html" : "text/plain");
    headers += "Content-Transfer-Encoding: 8bit\r\n\r\n";

    QByteArray data = headers.toUtf8() + body.toUtf8() + "\r\n.\r\n";
    if (sock.write(data) != data.size() || !sock.waitForBytesWritten(10000)) { if (err) *err = "数据写入失败"; return false; }
    if (!readReply(sock, code, reply) || code != 250) { if (err) *err = reply; return false; }

    writeCmd(sock, "QUIT");
    readReply(sock, code, reply);
    logf("邮件发送完成");

    if (serverLog) *serverLog = log;
    return true;
}
