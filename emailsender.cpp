#include "emailsender.h"

static QByteArray crlfize(const QByteArray& in) {
    QByteArray out;
    out.reserve(in.size() + 32);
    for (int i=0; i<in.size(); ++i) {
        char c = in[i];
        if (c == '\n' && (i==0 || in[i-1] != '\r')) out.append('\r');
        out.append(c);
    }
    return out;
}

QString EmailSender::encodeHeaderUtf8(const QString& s) {
    // RFC 2047: =?UTF-8?B?<base64>?=
    return QStringLiteral("=?UTF-8?B?%1?=").arg(QString::fromLatin1(s.toUtf8().toBase64()));
}

bool EmailSender::writeCmd(QIODevice& sock, const QByteArray& cmd) {
    QByteArray data = cmd;
    if (!data.endsWith("\r\n")) data += "\r\n";
    qint64 n = sock.write(data);
    return n == data.size() && sock.waitForBytesWritten(10000);
}

bool EmailSender::readReply(QIODevice& sock, int& code, QString& lines, int timeoutMs) {
    lines.clear();
    if (!sock.waitForReadyRead(timeoutMs)) return false;
    while (sock.bytesAvailable() || sock.waitForReadyRead(30)) {
        QByteArray line = sock.readLine();
        if (line.isEmpty()) break;
        lines += QString::fromLatin1(line);
        if (line.size() >= 4 && line[3] == ' ') break; // 结束行："250 ..."
    }
    if (lines.size() < 3) return false;
    code = lines.leftRef(3).toInt();
    return true;
}

EmailSender::EmailSender(const SmtpConfig& cfg, QObject* parent)
    : QObject(parent), cfg_(cfg) {}

bool EmailSender::send(const QStringList& to,
                       const QString& subject,
                       const QString& body,
                       bool isHtml,
                       QString* serverLog,
                       QString* err) {
    QString log;
    auto logf = [&](const QString& s){ log += s + "\n"; };

    if (to.isEmpty()) { if (err) *err = "收件人为空"; return false; }

    QSslSocket sock;

    if (cfg_.useSsl) {
        // SSL 直连模式
        sock.connectToHostEncrypted(cfg_.host, cfg_.port);
        if (!sock.waitForEncrypted(15000)) {
            if (err) *err = "SSL 握手失败: " + sock.errorString();
            return false;
        }
        logf("SSL OK: " + sock.sessionCipher().name());
    } else {
        // 普通TCP连接模式
        sock.connectToHost(cfg_.host, cfg_.port);
        if (!sock.waitForConnected(15000)) {
            if (err) *err = "TCP 连接失败: " + sock.errorString();
            return false;
        }
        logf("TCP 连接成功");
    }

    int code; QString reply;

    // 220 Greeting
    if (!readReply(sock, code, reply) || code != 220) {
        if (err) *err = "服务端问候失败: " + reply;
        if (serverLog) *serverLog = log + reply;
        return false;
    }
    logf(reply.trimmed());

    // EHLO
    if (!writeCmd(sock, "EHLO localhost")) { if (err) *err = "发送 EHLO 失败"; return false; }
    if (!readReply(sock, code, reply) || code != 250) {
        if (err) *err = "EHLO 失败: " + reply;
        if (serverLog) *serverLog = log + reply;
        return false;
    }
    logf(reply.trimmed());

    // AUTH LOGIN
    if (!writeCmd(sock, "AUTH LOGIN")) { if (err) *err = "发送 AUTH LOGIN 失败"; return false; }
    if (!readReply(sock, code, reply) || code != 334) { if (err) *err = "AUTH LOGIN 未被接受: " + reply; if (serverLog) *serverLog = log + reply; return false; }

    if (!writeCmd(sock, cfg_.username.toUtf8().toBase64())) { if (err) *err = "发送用户名失败"; return false; }
    if (!readReply(sock, code, reply) || code != 334) { if (err) *err = "用户名阶段失败: " + reply; if (serverLog) *serverLog = log + reply; return false; }

    if (!writeCmd(sock, cfg_.authCode.toUtf8().toBase64())) { if (err) *err = "发送授权码失败"; return false; }
    if (!readReply(sock, code, reply) || code != 235) { if (err) *err = "认证失败: " + reply; if (serverLog) *serverLog = log + reply; return false; }
    logf("认证通过");

    // MAIL FROM
    if (!writeCmd(sock, "MAIL FROM:<" + cfg_.from.toUtf8() + ">")) { if (err) *err = "MAIL FROM 失败"; return false; }
    if (!readReply(sock, code, reply) || code != 250) { if (err) *err = "MAIL FROM 被拒绝: " + reply; if (serverLog) *serverLog = log + reply; return false; }

    // RCPT TO
    for (const QString& rcpt : to) {
        if (!writeCmd(sock, "RCPT TO:<" + rcpt.toUtf8() + ">")) { if (err) *err = "RCPT TO 发送失败"; return false; }
        if (!readReply(sock, code, reply) || (code != 250 && code != 251)) { if (err) *err = "收件人拒收: " + rcpt + " | " + reply; if (serverLog) *serverLog = log + reply; return false; }
    }

    // DATA
    if (!writeCmd(sock, "DATA")) { if (err) *err = "DATA 发送失败"; return false; }
    if (!readReply(sock, code, reply) || code != 354) { if (err) *err = "DATA 阶段失败: " + reply; if (serverLog) *serverLog = log + reply; return false; }

    // Headers
    QString fromName = cfg_.fromName.trimmed().isEmpty() ? cfg_.from : encodeHeaderUtf8(cfg_.fromName);
    QString headers;
    headers += "From: " + (cfg_.fromName.isEmpty() ? QString("<%1>").arg(cfg_.from)
                                                   : QString("%1 <%2>").arg(fromName, cfg_.from)) + "\r\n";
    headers += "To: " + to.join(", ") + "\r\n";
    headers += "Subject: " + encodeHeaderUtf8(subject) + "\r\n";
    headers += "MIME-Version: 1.0\r\n";
    headers += QString("Content-Type: %1; charset=UTF-8\r\n").arg(isHtml ? "text/html" : "text/plain");
    headers += "Content-Transfer-Encoding: 8bit\r\n";
    headers += "\r\n";

    QByteArray data = headers.toUtf8();

    // Dot-stuffing：正文中以 '.' 开头的行需要前置一个 '.'，避免被提前截断
    QByteArray bodyUtf8 = body.toUtf8();
    bodyUtf8 = crlfize(bodyUtf8);
    QList<QByteArray> lines = bodyUtf8.split('\n');
    QByteArray stuffed;
    stuffed.reserve(bodyUtf8.size()+16);
    for (int i=0;i<lines.size();++i) {
        QByteArray ln = lines[i];
        if (!ln.endsWith('\r')) ln.append('\r'); // 确保 CRLF
        if (!ln.isEmpty() && ln[0]=='.') stuffed.append('.');
        stuffed.append(ln);
        stuffed.append('\n');
    }
    data += stuffed;
    data += "\r\n.\r\n";

    if (sock.write(data) != data.size() || !sock.waitForBytesWritten(15000)) {
        if (err) *err = "写入邮件数据失败";
        return false;
    }
    if (!readReply(sock, code, reply) || code != 250) {
        if (err) *err = "发送邮件体失败: " + reply;
        if (serverLog) *serverLog = log + reply;
        return false;
    }

    writeCmd(sock, "QUIT");
    readReply(sock, code, reply);
    logf("发送完成");

    if (serverLog) *serverLog = log;
    return true;
}
