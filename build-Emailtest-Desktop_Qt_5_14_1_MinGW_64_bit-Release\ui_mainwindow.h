/********************************************************************************
** Form generated from reading UI file 'mainwindow.ui'
**
** Created by: Qt User Interface Compiler version 5.14.1
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MAINWINDOW_H
#define UI_MAINWINDOW_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QCheckBox>
#include <QtWidgets/QFormLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QMenuBar>
#include <QtWidgets/QPlainTextEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QSpinBox>
#include <QtWidgets/QStatusBar>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_MainWindow
{
public:
    QWidget *centralwidget;
    QVBoxLayout *verticalLayout;
    QFormLayout *formLayout;
    QLabel *labelHost;
    QLineEdit *edtHost;
    QLabel *labelPort;
    QSpinBox *spnPort;
    QCheckBox *chkSSL;
    QLabel *labelUser;
    QLineEdit *edtUser;
    QLabel *labelAuth;
    QLineEdit *edtAuth;
    QCheckBox *chkShowAuth;
    QLabel *labelFrom;
    QLineEdit *edtFrom;
    QLabel *labelFromName;
    QLineEdit *edtFromName;
    QLabel *labelTo;
    QLineEdit *edtTo;
    QLabel *labelSubject;
    QLineEdit *edtSubject;
    QCheckBox *chkHtml;
    QLabel *labelBody;
    QPlainTextEdit *txtBody;
    QHBoxLayout *buttonLayout;
    QPushButton *btnSend;
    QPushButton *btnClearLog;
    QSpacerItem *horizontalSpacer;
    QPlainTextEdit *txtLog;
    QMenuBar *menubar;
    QStatusBar *statusbar;

    void setupUi(QMainWindow *MainWindow)
    {
        if (MainWindow->objectName().isEmpty())
            MainWindow->setObjectName(QString::fromUtf8("MainWindow"));
        MainWindow->resize(900, 700);
        centralwidget = new QWidget(MainWindow);
        centralwidget->setObjectName(QString::fromUtf8("centralwidget"));
        verticalLayout = new QVBoxLayout(centralwidget);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        formLayout = new QFormLayout();
        formLayout->setObjectName(QString::fromUtf8("formLayout"));
        labelHost = new QLabel(centralwidget);
        labelHost->setObjectName(QString::fromUtf8("labelHost"));

        formLayout->setWidget(0, QFormLayout::LabelRole, labelHost);

        edtHost = new QLineEdit(centralwidget);
        edtHost->setObjectName(QString::fromUtf8("edtHost"));

        formLayout->setWidget(0, QFormLayout::FieldRole, edtHost);

        labelPort = new QLabel(centralwidget);
        labelPort->setObjectName(QString::fromUtf8("labelPort"));

        formLayout->setWidget(0, QFormLayout::FieldRole, labelPort);

        spnPort = new QSpinBox(centralwidget);
        spnPort->setObjectName(QString::fromUtf8("spnPort"));
        spnPort->setMaximum(65535);
        spnPort->setValue(465);

        formLayout->setWidget(0, QFormLayout::FieldRole, spnPort);

        chkSSL = new QCheckBox(centralwidget);
        chkSSL->setObjectName(QString::fromUtf8("chkSSL"));
        chkSSL->setChecked(true);

        formLayout->setWidget(0, QFormLayout::FieldRole, chkSSL);

        labelUser = new QLabel(centralwidget);
        labelUser->setObjectName(QString::fromUtf8("labelUser"));

        formLayout->setWidget(1, QFormLayout::LabelRole, labelUser);

        edtUser = new QLineEdit(centralwidget);
        edtUser->setObjectName(QString::fromUtf8("edtUser"));

        formLayout->setWidget(1, QFormLayout::SpanningRole, edtUser);

        labelAuth = new QLabel(centralwidget);
        labelAuth->setObjectName(QString::fromUtf8("labelAuth"));

        formLayout->setWidget(2, QFormLayout::LabelRole, labelAuth);

        edtAuth = new QLineEdit(centralwidget);
        edtAuth->setObjectName(QString::fromUtf8("edtAuth"));
        edtAuth->setEchoMode(QLineEdit::Password);

        formLayout->setWidget(2, QFormLayout::SpanningRole, edtAuth);

        chkShowAuth = new QCheckBox(centralwidget);
        chkShowAuth->setObjectName(QString::fromUtf8("chkShowAuth"));

        formLayout->setWidget(2, QFormLayout::FieldRole, chkShowAuth);

        labelFrom = new QLabel(centralwidget);
        labelFrom->setObjectName(QString::fromUtf8("labelFrom"));

        formLayout->setWidget(3, QFormLayout::LabelRole, labelFrom);

        edtFrom = new QLineEdit(centralwidget);
        edtFrom->setObjectName(QString::fromUtf8("edtFrom"));

        formLayout->setWidget(3, QFormLayout::SpanningRole, edtFrom);

        labelFromName = new QLabel(centralwidget);
        labelFromName->setObjectName(QString::fromUtf8("labelFromName"));

        formLayout->setWidget(3, QFormLayout::FieldRole, labelFromName);

        edtFromName = new QLineEdit(centralwidget);
        edtFromName->setObjectName(QString::fromUtf8("edtFromName"));

        formLayout->setWidget(3, QFormLayout::FieldRole, edtFromName);

        labelTo = new QLabel(centralwidget);
        labelTo->setObjectName(QString::fromUtf8("labelTo"));

        formLayout->setWidget(4, QFormLayout::LabelRole, labelTo);

        edtTo = new QLineEdit(centralwidget);
        edtTo->setObjectName(QString::fromUtf8("edtTo"));

        formLayout->setWidget(4, QFormLayout::SpanningRole, edtTo);

        labelSubject = new QLabel(centralwidget);
        labelSubject->setObjectName(QString::fromUtf8("labelSubject"));

        formLayout->setWidget(5, QFormLayout::LabelRole, labelSubject);

        edtSubject = new QLineEdit(centralwidget);
        edtSubject->setObjectName(QString::fromUtf8("edtSubject"));

        formLayout->setWidget(5, QFormLayout::SpanningRole, edtSubject);

        chkHtml = new QCheckBox(centralwidget);
        chkHtml->setObjectName(QString::fromUtf8("chkHtml"));
        chkHtml->setChecked(true);

        formLayout->setWidget(5, QFormLayout::FieldRole, chkHtml);

        labelBody = new QLabel(centralwidget);
        labelBody->setObjectName(QString::fromUtf8("labelBody"));

        formLayout->setWidget(6, QFormLayout::LabelRole, labelBody);

        txtBody = new QPlainTextEdit(centralwidget);
        txtBody->setObjectName(QString::fromUtf8("txtBody"));

        formLayout->setWidget(6, QFormLayout::SpanningRole, txtBody);


        verticalLayout->addLayout(formLayout);

        buttonLayout = new QHBoxLayout();
        buttonLayout->setObjectName(QString::fromUtf8("buttonLayout"));
        btnSend = new QPushButton(centralwidget);
        btnSend->setObjectName(QString::fromUtf8("btnSend"));

        buttonLayout->addWidget(btnSend);

        btnClearLog = new QPushButton(centralwidget);
        btnClearLog->setObjectName(QString::fromUtf8("btnClearLog"));

        buttonLayout->addWidget(btnClearLog);

        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        buttonLayout->addItem(horizontalSpacer);


        verticalLayout->addLayout(buttonLayout);

        txtLog = new QPlainTextEdit(centralwidget);
        txtLog->setObjectName(QString::fromUtf8("txtLog"));
        txtLog->setReadOnly(true);

        verticalLayout->addWidget(txtLog);

        MainWindow->setCentralWidget(centralwidget);
        menubar = new QMenuBar(MainWindow);
        menubar->setObjectName(QString::fromUtf8("menubar"));
        MainWindow->setMenuBar(menubar);
        statusbar = new QStatusBar(MainWindow);
        statusbar->setObjectName(QString::fromUtf8("statusbar"));
        MainWindow->setStatusBar(statusbar);

        retranslateUi(MainWindow);

        QMetaObject::connectSlotsByName(MainWindow);
    } // setupUi

    void retranslateUi(QMainWindow *MainWindow)
    {
        MainWindow->setWindowTitle(QCoreApplication::translate("MainWindow", "\351\202\256\344\273\266\345\217\221\351\200\201\346\265\213\350\257\225 - \346\257\217\346\227\245\345\276\205\345\212\236", nullptr));
        labelHost->setText(QCoreApplication::translate("MainWindow", "SMTP\346\234\215\345\212\241\345\231\250", nullptr));
        labelPort->setText(QCoreApplication::translate("MainWindow", "\347\253\257\345\217\243", nullptr));
        chkSSL->setText(QCoreApplication::translate("MainWindow", "SSL", nullptr));
        labelUser->setText(QCoreApplication::translate("MainWindow", "\347\224\250\346\210\267\345\220\215", nullptr));
        labelAuth->setText(QCoreApplication::translate("MainWindow", "\346\216\210\346\235\203\347\240\201", nullptr));
        chkShowAuth->setText(QCoreApplication::translate("MainWindow", "\346\230\276\347\244\272", nullptr));
        labelFrom->setText(QCoreApplication::translate("MainWindow", "From", nullptr));
        labelFromName->setText(QCoreApplication::translate("MainWindow", "\345\217\221\344\273\266\344\272\272\345\220\215", nullptr));
        labelTo->setText(QCoreApplication::translate("MainWindow", "\346\224\266\344\273\266\344\272\272", nullptr));
        labelSubject->setText(QCoreApplication::translate("MainWindow", "\344\270\273\351\242\230", nullptr));
        chkHtml->setText(QCoreApplication::translate("MainWindow", "HTML", nullptr));
        labelBody->setText(QCoreApplication::translate("MainWindow", "\346\255\243\346\226\207", nullptr));
        btnSend->setText(QCoreApplication::translate("MainWindow", "\345\217\221\351\200\201", nullptr));
        btnClearLog->setText(QCoreApplication::translate("MainWindow", "\346\270\205\347\251\272\346\227\245\345\277\227", nullptr));
    } // retranslateUi

};

namespace Ui {
    class MainWindow: public Ui_MainWindow {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MAINWINDOW_H
