/********************************************************************************
** Form generated from reading UI file 'mainwindow.ui'
**
** Created by: Qt User Interface Compiler version 5.14.1
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MAINWINDOW_H
#define UI_MAINWINDOW_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QListWidget>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QMenuBar>
#include <QtWidgets/QPlainTextEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QStatusBar>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_MainWindow
{
public:
    QWidget *centralwidget;
    QVBoxLayout *verticalLayout;
    QGroupBox *groupSender;
    QGridLayout *senderLayout;
    QLabel *labelSenderEmail;
    QLineEdit *edtSenderEmail;
    QLabel *labelSenderName;
    QLineEdit *edtSenderName;
    QGroupBox *groupRecipients;
    QVBoxLayout *recipientsLayout;
    QHBoxLayout *recipientControlLayout;
    QLineEdit *edtNewRecipient;
    QPushButton *btnAddRecipient;
    QPushButton *btnRemoveRecipient;
    QListWidget *listRecipients;
    QGroupBox *groupContent;
    QVBoxLayout *contentLayout;
    QHBoxLayout *subjectLayout;
    QLabel *labelSubject;
    QLineEdit *edtSubject;
    QLabel *labelBody;
    QPlainTextEdit *txtEmailBody;
    QHBoxLayout *buttonLayout;
    QSpacerItem *buttonSpacer;
    QPushButton *btnSend;
    QGroupBox *groupLog;
    QVBoxLayout *logLayout;
    QHBoxLayout *logControlLayout;
    QPushButton *btnClearLog;
    QSpacerItem *logSpacer;
    QPlainTextEdit *txtLog;
    QMenuBar *menubar;
    QStatusBar *statusbar;

    void setupUi(QMainWindow *MainWindow)
    {
        if (MainWindow->objectName().isEmpty())
            MainWindow->setObjectName(QString::fromUtf8("MainWindow"));
        MainWindow->resize(800, 734);
        centralwidget = new QWidget(MainWindow);
        centralwidget->setObjectName(QString::fromUtf8("centralwidget"));
        verticalLayout = new QVBoxLayout(centralwidget);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        groupSender = new QGroupBox(centralwidget);
        groupSender->setObjectName(QString::fromUtf8("groupSender"));
        senderLayout = new QGridLayout(groupSender);
        senderLayout->setObjectName(QString::fromUtf8("senderLayout"));
        labelSenderEmail = new QLabel(groupSender);
        labelSenderEmail->setObjectName(QString::fromUtf8("labelSenderEmail"));

        senderLayout->addWidget(labelSenderEmail, 0, 0, 1, 1);

        edtSenderEmail = new QLineEdit(groupSender);
        edtSenderEmail->setObjectName(QString::fromUtf8("edtSenderEmail"));

        senderLayout->addWidget(edtSenderEmail, 0, 1, 1, 1);

        labelSenderName = new QLabel(groupSender);
        labelSenderName->setObjectName(QString::fromUtf8("labelSenderName"));

        senderLayout->addWidget(labelSenderName, 0, 2, 1, 1);

        edtSenderName = new QLineEdit(groupSender);
        edtSenderName->setObjectName(QString::fromUtf8("edtSenderName"));

        senderLayout->addWidget(edtSenderName, 0, 3, 1, 1);


        verticalLayout->addWidget(groupSender);

        groupRecipients = new QGroupBox(centralwidget);
        groupRecipients->setObjectName(QString::fromUtf8("groupRecipients"));
        recipientsLayout = new QVBoxLayout(groupRecipients);
        recipientsLayout->setObjectName(QString::fromUtf8("recipientsLayout"));
        recipientControlLayout = new QHBoxLayout();
        recipientControlLayout->setObjectName(QString::fromUtf8("recipientControlLayout"));
        edtNewRecipient = new QLineEdit(groupRecipients);
        edtNewRecipient->setObjectName(QString::fromUtf8("edtNewRecipient"));

        recipientControlLayout->addWidget(edtNewRecipient);

        btnAddRecipient = new QPushButton(groupRecipients);
        btnAddRecipient->setObjectName(QString::fromUtf8("btnAddRecipient"));

        recipientControlLayout->addWidget(btnAddRecipient);

        btnRemoveRecipient = new QPushButton(groupRecipients);
        btnRemoveRecipient->setObjectName(QString::fromUtf8("btnRemoveRecipient"));

        recipientControlLayout->addWidget(btnRemoveRecipient);


        recipientsLayout->addLayout(recipientControlLayout);

        listRecipients = new QListWidget(groupRecipients);
        listRecipients->setObjectName(QString::fromUtf8("listRecipients"));
        listRecipients->setSelectionMode(QAbstractItemView::MultiSelection);

        recipientsLayout->addWidget(listRecipients);


        verticalLayout->addWidget(groupRecipients);

        groupContent = new QGroupBox(centralwidget);
        groupContent->setObjectName(QString::fromUtf8("groupContent"));
        contentLayout = new QVBoxLayout(groupContent);
        contentLayout->setObjectName(QString::fromUtf8("contentLayout"));
        subjectLayout = new QHBoxLayout();
        subjectLayout->setObjectName(QString::fromUtf8("subjectLayout"));
        labelSubject = new QLabel(groupContent);
        labelSubject->setObjectName(QString::fromUtf8("labelSubject"));

        subjectLayout->addWidget(labelSubject);

        edtSubject = new QLineEdit(groupContent);
        edtSubject->setObjectName(QString::fromUtf8("edtSubject"));

        subjectLayout->addWidget(edtSubject);


        contentLayout->addLayout(subjectLayout);

        labelBody = new QLabel(groupContent);
        labelBody->setObjectName(QString::fromUtf8("labelBody"));

        contentLayout->addWidget(labelBody);

        txtEmailBody = new QPlainTextEdit(groupContent);
        txtEmailBody->setObjectName(QString::fromUtf8("txtEmailBody"));

        contentLayout->addWidget(txtEmailBody);

        buttonLayout = new QHBoxLayout();
        buttonLayout->setObjectName(QString::fromUtf8("buttonLayout"));
        buttonSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        buttonLayout->addItem(buttonSpacer);

        btnSend = new QPushButton(groupContent);
        btnSend->setObjectName(QString::fromUtf8("btnSend"));

        buttonLayout->addWidget(btnSend);


        contentLayout->addLayout(buttonLayout);


        verticalLayout->addWidget(groupContent);

        groupLog = new QGroupBox(centralwidget);
        groupLog->setObjectName(QString::fromUtf8("groupLog"));
        logLayout = new QVBoxLayout(groupLog);
        logLayout->setObjectName(QString::fromUtf8("logLayout"));
        logControlLayout = new QHBoxLayout();
        logControlLayout->setObjectName(QString::fromUtf8("logControlLayout"));
        btnClearLog = new QPushButton(groupLog);
        btnClearLog->setObjectName(QString::fromUtf8("btnClearLog"));

        logControlLayout->addWidget(btnClearLog);

        logSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        logControlLayout->addItem(logSpacer);


        logLayout->addLayout(logControlLayout);

        txtLog = new QPlainTextEdit(groupLog);
        txtLog->setObjectName(QString::fromUtf8("txtLog"));
        QFont font;
        font.setFamily(QString::fromUtf8("Consolas"));
        font.setPointSize(9);
        txtLog->setFont(font);
        txtLog->setReadOnly(true);

        logLayout->addWidget(txtLog);


        verticalLayout->addWidget(groupLog);

        MainWindow->setCentralWidget(centralwidget);
        menubar = new QMenuBar(MainWindow);
        menubar->setObjectName(QString::fromUtf8("menubar"));
        menubar->setGeometry(QRect(0, 0, 800, 26));
        MainWindow->setMenuBar(menubar);
        statusbar = new QStatusBar(MainWindow);
        statusbar->setObjectName(QString::fromUtf8("statusbar"));
        MainWindow->setStatusBar(statusbar);

        retranslateUi(MainWindow);

        QMetaObject::connectSlotsByName(MainWindow);
    } // setupUi

    void retranslateUi(QMainWindow *MainWindow)
    {
        MainWindow->setWindowTitle(QCoreApplication::translate("MainWindow", "\347\256\200\346\230\223\351\202\256\344\273\266\345\217\221\351\200\201\345\231\250", nullptr));
        groupSender->setTitle(QCoreApplication::translate("MainWindow", "\345\217\221\344\273\266\344\272\272\344\277\241\346\201\257", nullptr));
        labelSenderEmail->setText(QCoreApplication::translate("MainWindow", "\345\217\221\344\273\266\351\202\256\347\256\261:", nullptr));
        edtSenderEmail->setPlaceholderText(QCoreApplication::translate("MainWindow", "<EMAIL>", nullptr));
        labelSenderName->setText(QCoreApplication::translate("MainWindow", "\345\217\221\344\273\266\344\272\272\345\220\215\347\247\260:", nullptr));
        edtSenderName->setPlaceholderText(QCoreApplication::translate("MainWindow", "\345\274\240\344\270\211", nullptr));
        groupRecipients->setTitle(QCoreApplication::translate("MainWindow", "\346\224\266\344\273\266\344\272\272\344\277\241\346\201\257", nullptr));
        edtNewRecipient->setPlaceholderText(QCoreApplication::translate("MainWindow", "\350\276\223\345\205\245\346\224\266\344\273\266\344\272\272\351\202\256\347\256\261\345\234\260\345\235\200\357\274\214\346\214\211\345\233\236\350\275\246\346\267\273\345\212\240", nullptr));
        btnAddRecipient->setText(QCoreApplication::translate("MainWindow", "\346\267\273\345\212\240", nullptr));
        btnRemoveRecipient->setText(QCoreApplication::translate("MainWindow", "\345\210\240\351\231\244\351\200\211\344\270\255", nullptr));
        groupContent->setTitle(QCoreApplication::translate("MainWindow", "\351\202\256\344\273\266\345\206\205\345\256\271", nullptr));
        labelSubject->setText(QCoreApplication::translate("MainWindow", "\351\202\256\344\273\266\344\270\273\351\242\230:", nullptr));
        edtSubject->setPlaceholderText(QCoreApplication::translate("MainWindow", "\350\257\267\350\276\223\345\205\245\351\202\256\344\273\266\344\270\273\351\242\230", nullptr));
        labelBody->setText(QCoreApplication::translate("MainWindow", "\351\202\256\344\273\266\346\255\243\346\226\207:", nullptr));
        txtEmailBody->setPlaceholderText(QCoreApplication::translate("MainWindow", "\350\257\267\350\276\223\345\205\245\351\202\256\344\273\266\346\255\243\346\226\207\345\206\205\345\256\271...", nullptr));
        btnSend->setStyleSheet(QCoreApplication::translate("MainWindow", "QPushButton {\n"
"    background-color: #4CAF50;\n"
"    color: white;\n"
"    border: none;\n"
"    padding: 8px 16px;\n"
"    border-radius: 4px;\n"
"    font-weight: bold;\n"
"    font-size: 14px;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #45a049;\n"
"}\n"
"QPushButton:pressed {\n"
"    background-color: #3d8b40;\n"
"}\n"
"QPushButton:disabled {\n"
"    background-color: #cccccc;\n"
"    color: #666666;\n"
"}", nullptr));
        btnSend->setText(QCoreApplication::translate("MainWindow", "\345\217\221\351\200\201\351\202\256\344\273\266", nullptr));
        groupLog->setTitle(QCoreApplication::translate("MainWindow", "\345\217\221\351\200\201\346\227\245\345\277\227", nullptr));
        btnClearLog->setText(QCoreApplication::translate("MainWindow", "\346\270\205\347\251\272\346\227\245\345\277\227", nullptr));
        txtLog->setStyleSheet(QCoreApplication::translate("MainWindow", "QPlainTextEdit {\n"
"    background-color: #f5f5f5;\n"
"    border: 1px solid #ddd;\n"
"    border-radius: 4px;\n"
"}", nullptr));
    } // retranslateUi

};

namespace Ui {
    class MainWindow: public Ui_MainWindow {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MAINWINDOW_H
