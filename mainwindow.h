#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QSettings>
#include "emailsender.h"

QT_BEGIN_NAMESPACE
namespace Ui { class MainWindow; }
QT_END_NAMESPACE

class MainWindow : public QMainWindow {
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    void on_btnSend_clicked();
    void on_btnClearLog_clicked();
    void on_chkShowAuth_toggled(bool checked);

private:
    Ui::MainWindow *ui;
    QSettings settings_{"YourCompany", "EmailTester"};
    void loadSettings();
    void saveSettings();
    void appendLog(const QString& text);
};

#endif // MAINWINDOW_H
