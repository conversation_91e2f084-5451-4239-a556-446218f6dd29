#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QMessageBox>
#include <QThread>
#include <QTimer>
#include <QSettings>
#include <QKeyEvent>
#include "emailsender.h"

QT_BEGIN_NAMESPACE
namespace Ui { class MainWindow; }
QT_END_NAMESPACE

// 邮件发送工作线程
class EmailWorker : public QObject {
    Q_OBJECT

public:
    EmailWorker(const SmtpConfig& config,
                const QStringList& to,
                const QString& subject,
                const QString& body);

public slots:
    void sendEmail();

signals:
    void emailSent(bool success, const QString& message, const QString& serverLog);

private:
    SmtpConfig config_;
    QStringList to_;
    QString subject_;
    QString body_;
};

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

protected:
    bool eventFilter(QObject *obj, QEvent *event) override;

private slots:
    void on_btnSend_clicked();
    void on_btnClearLog_clicked();
    void on_btnAddRecipient_clicked();
    void on_btnRemoveRecipient_clicked();

    // 邮件发送结果处理
    void onEmailSent(bool success, const QString& message, const QString& serverLog);

private:
    Ui::MainWindow *ui;
    QThread* emailThread_;
    EmailWorker* emailWorker_;
    QSettings* settings_;

    // 界面初始化
    void initializeUI();
    void loadSettings();
    void saveSettings();

    // 输入验证
    bool validateInputs();

    // 日志输出
    void appendLog(const QString& message, bool isError = false);

    // 获取配置
    SmtpConfig getSmtpConfig();
    QStringList getRecipients();

    // 添加收件人（支持回车键）
    void addRecipientFromInput();
};

#endif // MAINWINDOW_H
