#include <QCoreApplication>
#include "emailsender.h"

int main(int argc, char *argv[]) {
    QCoreApplication app(argc, argv);

    SmtpConfig cfg;
    cfg.username = "<EMAIL>";
    cfg.authCode = "qwqupsmylvsciahc";   // QQ 邮箱 SMTP 授权码
    cfg.from     = "<EMAIL>";
    cfg.fromName = "业务追踪系统";

    EmailSender sender(cfg);

    QStringList to = {"<EMAIL>"};
    QString subject = "Qt SMTP 测试 - 每日待办演示";
    QString body =
            "<h3>今日待办</h3>"
            "<ul>"
            "<li>编写测试工具</li>"
            "<li>验证 QQ 邮箱推送</li>"
            "<li>记录日志与失败重试策略</li>"
            "</ul>"
            "<p style='color:#888'>本邮件由测试程序发送。</p>";

    QString serverLog, err;
    bool ok = sender.send(to, subject, body, /*isHtml=*/true, &serverLog, &err);

    qInfo().noquote() << (ok ? "[OK] 邮件发送成功" : "[FAIL] 邮件发送失败: " + err);
    qInfo().noquote() << "---- 服务器交互日志 ----\n" << serverLog;

    return 0;
}
