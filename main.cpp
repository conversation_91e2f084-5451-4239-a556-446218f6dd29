#include <QCoreApplication>
#include <QtNetwork/QSslSocket>
#include <QtNetwork/QSslConfiguration>
#include <QtNetwork/QSslCertificate>
#include <QtNetwork/QSslCipher>
#include <QDebug>
#include "emailsender.h"

// 检查 TLS/SSL 支持情况，并设置最低协议版本
static bool sslSelfCheck() {
    qInfo() << "==== SSL/TLS 环境自检 ====";
    qInfo() << "supportsSsl =" << QSslSocket::supportsSsl();
    qInfo() << "build SSL   =" << QSslSocket::sslLibraryBuildVersionString();
    qInfo() << "runtime SSL =" << QSslSocket::sslLibraryVersionString();

    if (!QSslSocket::supportsSsl()) {
        qCritical() << "[错误] 当前 Qt 无法使用 SSL，请检查是否缺少 OpenSSL 运行库或 tls 插件";
        return false;
    }

    // 设置最低协议版本为 TLS 1.2
#if QT_VERSION >= QT_VERSION_CHECK(5, 12, 0)
    QSslConfiguration conf = QSslConfiguration::defaultConfiguration();
    conf.setProtocol(QSsl::TlsV1_2OrLater);
    QSslConfiguration::setDefaultConfiguration(conf);
#else
    QSslConfiguration conf = QSslConfiguration::defaultConfiguration();
    conf.setProtocol(QSsl::TlsV1_2);
    QSslConfiguration::setDefaultConfiguration(conf);
#endif
    return true;
}

int main(int argc, char *argv[]) {
    QCoreApplication app(argc, argv);

    if (!sslSelfCheck()) {
        qCritical() << "环境 SSL 检查未通过，程序退出";
        return 1;
    }

    SmtpConfig cfg;
    cfg.username = "<EMAIL>";
    cfg.authCode = "qwqupsmylvsciahc";   // QQ 邮箱 SMTP 授权码
    cfg.from     = "<EMAIL>";
    cfg.fromName = "业务追踪系统";

    EmailSender sender(cfg);

    QStringList to = {"<EMAIL>"};
    QString subject = "Qt SMTP 测试 - 每日待办演示";
    QString body =
        "<h3>今日待办</h3>"
        "<ul>"
        "<li>编写测试工具</li>"
        "<li>验证 QQ 邮箱推送</li>"
        "<li>记录日志与失败重试策略</li>"
        "</ul>"
        "<p style='color:#888'>本邮件由测试程序发送。</p>";

    QString serverLog, err;
    bool ok = sender.send(to, subject, body, /*isHtml=*/true, &serverLog, &err);

    qInfo().noquote() << (ok ? "[OK] 邮件发送成功" : "[FAIL] 邮件发送失败: " + err);
    qInfo().noquote() << "---- 服务器交互日志 ----\n" << serverLog;

    return 0;
}
