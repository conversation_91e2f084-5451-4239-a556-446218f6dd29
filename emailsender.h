#ifndef EMAILSENDER_H
#define EMAILSENDER_H

#include <QtNetwork/QSslSocket>
#include <QtCore>

struct SmtpConfig {
    QString host = "smtp.qq.com";
    quint16 port = 465;      // SSL 直连常用
    bool useSsl = true;      // 本示例走 SSL 直连
    QString username;        // 通常为完整邮箱，比如 <EMAIL>
    QString authCode;        // QQ 邮箱 SMTP 授权码
    QString from;            // 发件人邮箱（建议与 username 一致）
    QString fromName;        // 发件人显示名（可中文）
};

class EmailSender : public QObject {
    Q_OBJECT
public:
    explicit EmailSender(const SmtpConfig& cfg, QObject* parent=nullptr);
    bool send(const QStringList& to,
              const QString& subject,
              const QString& body,
              bool isHtml,
              QString* serverLog = nullptr,
              QString* err = nullptr);
private:
    SmtpConfig cfg_;
    static QString encodeHeaderUtf8(const QString& s); // RFC 2047
    static bool writeCmd(QIODevice& sock, const QByteArray& cmd);
    static bool readReply(QIODevice& sock, int& code, QString& lines, int timeoutMs=10000);
};

#endif // EMAILSENDER_H
