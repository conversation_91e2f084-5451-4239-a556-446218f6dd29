#ifndef EMAILSENDER_H
#define EMAILSENDER_H

#include <QtNetwork/QSslSocket>
#include <QtNetwork/QSslCipher>
#include <QtCore>

struct SmtpConfig {
    QString host = "smtp.qq.com";
    quint16 port = 465;
    bool useSsl = true;      // 是否使用SSL
    QString username;
    QString authCode;
    QString from;
    QString fromName;
};

class EmailSender : public QObject {
    Q_OBJECT
public:
    explicit EmailSender(const SmtpConfig& cfg, QObject* parent=nullptr);
    bool send(const QStringList& to,
              const QString& subject,
              const QString& body,
              bool isHtml,
              QString* serverLog = nullptr,
              QString* err = nullptr);
private:
    SmtpConfig cfg_;
    static QString encodeHeaderUtf8(const QString& s);
    static bool writeCmd(QIODevice& sock, const QByteArray& cmd);
    static bool readReply(QIODevice& sock, int& code, QString& lines, int timeoutMs=10000);
};

#endif // EMAILSENDER_H
