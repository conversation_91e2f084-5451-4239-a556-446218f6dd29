#ifndef EMAILSENDER_H
#define EMAILSENDER_H

#include <QString>
#include <QStringList>
#include <QIODevice>

struct SmtpConfig {
    QString host;
    quint16 port = 465;
    bool useSsl = true; // SSL直连
    QString username;
    QString authCode;   // QQ邮箱SMTP授权码
    QString from;
    QString fromName;
};

class EmailSender {
public:
    explicit EmailSender(const SmtpConfig& cfg);
    bool send(const QStringList& to,
              const QString& subject,
              const QString& body,
              bool isHtml,
              QString* serverLog = nullptr,
              QString* err = nullptr);
private:
    SmtpConfig cfg_;
    static QString encodeHeaderUtf8(const QString& s);
    static bool writeCmd(QIODevice& sock, const QByteArray& cmd);
    static bool readReply(QIODevice& sock, int& code, QString& lines, int timeoutMs = 10000);
};

#endif // EMAILSENDER_H
