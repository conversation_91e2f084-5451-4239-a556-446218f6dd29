<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1000</width>
    <height>800</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>智能邮件发送器</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout">
    <item>
     <widget class="QTabWidget" name="tabWidget">
      <property name="currentIndex">
       <number>0</number>
      </property>
      <widget class="QWidget" name="tabCompose">
       <attribute name="title">
        <string>编写邮件</string>
       </attribute>
       <layout class="QVBoxLayout" name="composeLayout">
        <item>
         <widget class="QGroupBox" name="groupSender">
          <property name="title">
           <string>发件人信息</string>
          </property>
          <layout class="QGridLayout" name="senderLayout">
           <item row="0" column="0">
            <widget class="QLabel" name="labelSenderEmail">
             <property name="text">
              <string>发件邮箱:</string>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QLineEdit" name="edtSenderEmail">
             <property name="placeholderText">
              <string><EMAIL></string>
             </property>
            </widget>
           </item>
           <item row="0" column="2">
            <widget class="QLabel" name="labelSenderName">
             <property name="text">
              <string>发件人名称:</string>
             </property>
            </widget>
           </item>
           <item row="0" column="3">
            <widget class="QLineEdit" name="edtSenderName">
             <property name="placeholderText">
              <string>张三</string>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QLabel" name="labelPassword">
             <property name="text">
              <string>授权码:</string>
             </property>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="QLineEdit" name="edtPassword">
             <property name="echoMode">
              <enum>QLineEdit::Password</enum>
             </property>
             <property name="placeholderText">
              <string>SMTP授权码</string>
             </property>
            </widget>
           </item>
           <item row="1" column="2">
            <widget class="QCheckBox" name="chkShowPassword">
             <property name="text">
              <string>显示密码</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupRecipients">
          <property name="title">
           <string>收件人信息</string>
          </property>
          <layout class="QVBoxLayout" name="recipientsLayout">
           <item>
            <layout class="QHBoxLayout" name="recipientControlLayout">
             <item>
              <widget class="QLineEdit" name="edtNewRecipient">
               <property name="placeholderText">
                <string>输入收件人邮箱地址</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="btnAddRecipient">
               <property name="text">
                <string>添加</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="btnRemoveRecipient">
               <property name="text">
                <string>删除选中</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <widget class="QListWidget" name="listRecipients">
             <property name="selectionMode">
              <enum>QAbstractItemView::MultiSelection</enum>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupContent">
          <property name="title">
           <string>邮件内容</string>
          </property>
          <layout class="QVBoxLayout" name="contentLayout">
           <item>
            <layout class="QHBoxLayout" name="subjectLayout">
             <item>
              <widget class="QLabel" name="labelSubject">
               <property name="text">
                <string>主题:</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="edtSubject">
               <property name="placeholderText">
                <string>请输入邮件主题</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QCheckBox" name="chkHtmlMode">
               <property name="text">
                <string>HTML格式</string>
               </property>
               <property name="checked">
                <bool>true</bool>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <widget class="QPlainTextEdit" name="txtEmailBody">
             <property name="placeholderText">
              <string>请输入邮件正文内容...

支持HTML格式，例如：
&lt;h2&gt;标题&lt;/h2&gt;
&lt;p&gt;段落内容&lt;/p&gt;
&lt;ul&gt;
  &lt;li&gt;列表项1&lt;/li&gt;
  &lt;li&gt;列表项2&lt;/li&gt;
&lt;/ul&gt;</string>
             </property>
            </widget>
           </item>
           <item>
            <layout class="QHBoxLayout" name="templateLayout">
             <item>
              <widget class="QPushButton" name="btnSaveTemplate">
               <property name="text">
                <string>保存为模板</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="btnLoadTemplate">
               <property name="text">
                <string>加载模板</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="templateSpacer">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QPushButton" name="btnSend">
               <property name="styleSheet">
                <string>QPushButton {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #45a049;
}
QPushButton:pressed {
    background-color: #3d8b40;
}</string>
               </property>
               <property name="text">
                <string>发送邮件</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tabSettings">
       <attribute name="title">
        <string>SMTP设置</string>
       </attribute>
       <layout class="QVBoxLayout" name="settingsLayout">
        <item>
         <widget class="QGroupBox" name="groupSmtp">
          <property name="title">
           <string>SMTP服务器配置</string>
          </property>
          <layout class="QGridLayout" name="smtpLayout">
           <item row="0" column="0">
            <widget class="QLabel" name="labelProvider">
             <property name="text">
              <string>邮箱服务商:</string>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QComboBox" name="cmbEmailProvider">
             <item>
              <property name="text">
               <string>QQ邮箱</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string>163邮箱</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string>Gmail</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string>Outlook</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string>自定义</string>
              </property>
             </item>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QLabel" name="labelHost">
             <property name="text">
              <string>SMTP服务器:</string>
             </property>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="QLineEdit" name="edtHost">
             <property name="text">
              <string>smtp.qq.com</string>
             </property>
            </widget>
           </item>
           <item row="1" column="2">
            <widget class="QLabel" name="labelPort">
             <property name="text">
              <string>端口:</string>
             </property>
            </widget>
           </item>
           <item row="1" column="3">
            <widget class="QSpinBox" name="spnPort">
             <property name="minimum">
              <number>1</number>
             </property>
             <property name="maximum">
              <number>65535</number>
             </property>
             <property name="value">
              <number>465</number>
             </property>
            </widget>
           </item>
           <item row="2" column="0">
            <widget class="QCheckBox" name="chkSSL">
             <property name="text">
              <string>使用SSL加密</string>
             </property>
             <property name="checked">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="2" column="1">
            <widget class="QCheckBox" name="chkStartTLS">
             <property name="text">
              <string>使用STARTTLS</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <spacer name="settingsSpacer">
          <property name="orientation">
           <enum>Qt::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>40</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tabLog">
       <attribute name="title">
        <string>发送日志</string>
       </attribute>
       <layout class="QVBoxLayout" name="logLayout">
        <item>
         <layout class="QHBoxLayout" name="logControlLayout">
          <item>
           <widget class="QPushButton" name="btnClearLog">
            <property name="text">
             <string>清空日志</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="logSpacer">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item>
         <widget class="QPlainTextEdit" name="txtLog">
          <property name="font">
           <font>
            <family>Consolas</family>
            <pointsize>9</pointsize>
           </font>
          </property>
          <property name="readOnly">
           <bool>true</bool>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1000</width>
     <height>26</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>
