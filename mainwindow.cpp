#include "mainwindow.h"
#include "ui_mainwindow.h"
#include <QDateTime>

// EmailWorker 实现
EmailWorker::EmailWorker(const SmtpConfig& config,
                        const QStringList& to,
                        const QString& subject,
                        const QString& body)
    : config_(config), to_(to), subject_(subject), body_(body)
{
}

void EmailWorker::sendEmail()
{
    EmailSender sender(config_);
    QString serverLog, err;
    bool success = sender.send(to_, subject_, body_, false, &serverLog, &err);

    QString message = success ? "邮件发送成功！" : QString("邮件发送失败: %1").arg(err);
    emit emailSent(success, message, serverLog);
}

// MainWindow 实现
MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow)
    , emailThread_(nullptr)
    , emailWorker_(nullptr)
    , settings_(new QSettings("EmailSender", "Settings", this))
{
    ui->setupUi(this);
    initializeUI();
    loadSettings();
}

MainWindow::~MainWindow()
{
    saveSettings();
    if (emailThread_) {
        emailThread_->quit();
        emailThread_->wait();
    }
    delete ui;
}

void MainWindow::initializeUI()
{
    // 设置默认邮件内容
    ui->edtSubject->setText("测试邮件 - " + QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm"));
    ui->txtEmailBody->setPlainText("这是一封测试邮件。\n\n祝好！");

    // 安装事件过滤器，支持回车键添加收件人
    ui->edtNewRecipient->installEventFilter(this);

    // 初始日志
    appendLog("邮件发送器已启动，请填写相关信息后点击发送");
}

bool MainWindow::eventFilter(QObject *obj, QEvent *event)
{
    if (obj == ui->edtNewRecipient && event->type() == QEvent::KeyPress) {
        QKeyEvent *keyEvent = static_cast<QKeyEvent*>(event);
        if (keyEvent->key() == Qt::Key_Return || keyEvent->key() == Qt::Key_Enter) {
            addRecipientFromInput();
            return true;
        }
    }
    return QMainWindow::eventFilter(obj, event);
}

void MainWindow::loadSettings()
{
    ui->edtSenderEmail->setText(settings_->value("sender/email").toString());
    ui->edtSenderName->setText(settings_->value("sender/name").toString());
}

void MainWindow::saveSettings()
{
    settings_->setValue("sender/email", ui->edtSenderEmail->text());
    settings_->setValue("sender/name", ui->edtSenderName->text());
}

bool MainWindow::validateInputs()
{
    if (ui->edtSenderEmail->text().isEmpty()) {
        QMessageBox::warning(this, "输入错误", "请输入发件人邮箱地址！");
        ui->edtSenderEmail->setFocus();
        return false;
    }

    if (ui->listRecipients->count() == 0) {
        QMessageBox::warning(this, "输入错误", "请至少添加一个收件人！");
        ui->edtNewRecipient->setFocus();
        return false;
    }

    if (ui->edtSubject->text().isEmpty()) {
        QMessageBox::warning(this, "输入错误", "请输入邮件主题！");
        ui->edtSubject->setFocus();
        return false;
    }

    if (ui->txtEmailBody->toPlainText().isEmpty()) {
        QMessageBox::warning(this, "输入错误", "请输入邮件内容！");
        ui->txtEmailBody->setFocus();
        return false;
    }

    return true;
}

SmtpConfig MainWindow::getSmtpConfig()
{
    SmtpConfig config;
    // 使用固定的QQ邮箱配置
    config.host = "smtp.qq.com";
    config.port = 25;  // 使用您测试成功的配置
    config.useSsl = false;
    config.username = ui->edtSenderEmail->text();
    config.authCode = "qwqupsmylvsciahc";  // 您的授权码（实际使用时应该让用户输入）
    config.from = ui->edtSenderEmail->text();
    config.fromName = ui->edtSenderName->text();
    return config;
}

QStringList MainWindow::getRecipients()
{
    QStringList recipients;
    for (int i = 0; i < ui->listRecipients->count(); ++i) {
        recipients << ui->listRecipients->item(i)->text();
    }
    return recipients;
}

void MainWindow::appendLog(const QString& message, bool isError)
{
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
    QString logEntry = QString("[%1] %2").arg(timestamp, message);

    if (isError) {
        ui->txtLog->appendPlainText("❌ " + logEntry);
    } else {
        ui->txtLog->appendPlainText("✅ " + logEntry);
    }
}

void MainWindow::addRecipientFromInput()
{
    QString email = ui->edtNewRecipient->text().trimmed();
    if (!email.isEmpty() && email.contains("@")) {
        ui->listRecipients->addItem(email);
        ui->edtNewRecipient->clear();
        appendLog(QString("已添加收件人: %1").arg(email));
    } else if (!email.isEmpty()) {
        QMessageBox::warning(this, "输入错误", "请输入有效的邮箱地址！");
    }
}

// 槽函数实现
void MainWindow::on_btnSend_clicked()
{
    if (!validateInputs()) {
        return;
    }

    ui->btnSend->setEnabled(false);
    ui->btnSend->setText("发送中...");

    appendLog("开始发送邮件...");

    // 创建工作线程
    emailThread_ = new QThread(this);
    emailWorker_ = new EmailWorker(
        getSmtpConfig(),
        getRecipients(),
        ui->edtSubject->text(),
        ui->txtEmailBody->toPlainText()
    );

    emailWorker_->moveToThread(emailThread_);

    connect(emailThread_, &QThread::started, emailWorker_, &EmailWorker::sendEmail);
    connect(emailWorker_, &EmailWorker::emailSent, this, &MainWindow::onEmailSent);
    connect(emailWorker_, &EmailWorker::emailSent, emailThread_, &QThread::quit);
    connect(emailThread_, &QThread::finished, emailWorker_, &EmailWorker::deleteLater);
    connect(emailThread_, &QThread::finished, emailThread_, &QThread::deleteLater);

    emailThread_->start();
}

void MainWindow::onEmailSent(bool success, const QString& message, const QString& serverLog)
{
    ui->btnSend->setEnabled(true);
    ui->btnSend->setText("发送邮件");

    appendLog(message, !success);

    if (!serverLog.isEmpty()) {
        appendLog("服务器日志:");
        ui->txtLog->appendPlainText(serverLog);
    }

    if (success) {
        QMessageBox::information(this, "发送成功", "邮件发送成功！");
    } else {
        QMessageBox::critical(this, "发送失败", message);
    }

    emailThread_ = nullptr;
    emailWorker_ = nullptr;
}

void MainWindow::on_btnAddRecipient_clicked()
{
    addRecipientFromInput();
}

void MainWindow::on_btnRemoveRecipient_clicked()
{
    auto selectedItems = ui->listRecipients->selectedItems();
    for (auto item : selectedItems) {
        QString email = item->text();
        delete ui->listRecipients->takeItem(ui->listRecipients->row(item));
        appendLog(QString("已删除收件人: %1").arg(email));
    }
}

void MainWindow::on_btnClearLog_clicked()
{
    ui->txtLog->clear();
    appendLog("日志已清空");
}
