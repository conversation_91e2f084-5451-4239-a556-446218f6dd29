#include "mainwindow.h"
#include "ui_mainwindow.h"
#include <QtNetwork/QSslSocket>
#include <QtNetwork/QSslConfiguration>
#include <QDateTime>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow) {
    ui->setupUi(this);

    // 默认值
    ui->edtHost->setText("smtp.qq.com");
    ui->spnPort->setRange(1, 65535);
    ui->spnPort->setValue(465);
    ui->chkSSL->setChecked(true);
    ui->chkHtml->setChecked(true);

    loadSettings();

    // TLS 自检
    appendLog("==== SSL/TLS 自检 ====");
    appendLog(QString("supportsSsl = %1").arg(QSslSocket::supportsSsl()));
    appendLog("build SSL   = " + QSslSocket::sslLibraryBuildVersionString());
    appendLog("runtime SSL = " + QSslSocket::sslLibraryVersionString());

#if QT_VERSION >= QT_VERSION_CHECK(5, 12, 0)
    QSslConfiguration conf = QSslConfiguration::defaultConfiguration();
    conf.setProtocol(QSsl::TlsV1_2OrLater);
    QSslConfiguration::setDefaultConfiguration(conf);
#endif
}

MainWindow::~MainWindow() {
    saveSettings();
    delete ui;
}

void MainWindow::loadSettings() {
    ui->edtHost->setText(settings_.value("host", ui->edtHost->text()).toString());
    ui->spnPort->setValue(settings_.value("port", ui->spnPort->value()).toInt());
    ui->chkSSL->setChecked(settings_.value("ssl", ui->chkSSL->isChecked()).toBool());
    ui->edtUser->setText(settings_.value("user").toString());
    ui->edtFrom->setText(settings_.value("from").toString());
    ui->edtFromName->setText(settings_.value("fromName").toString());
    ui->edtTo->setText(settings_.value("to").toString());
    ui->chkHtml->setChecked(settings_.value("isHtml", ui->chkHtml->isChecked()).toBool());
}

void MainWindow::saveSettings() {
    settings_.setValue("host", ui->edtHost->text());
    settings_.setValue("port", ui->spnPort->value());
    settings_.setValue("ssl", ui->chkSSL->isChecked());
    settings_.setValue("user", ui->edtUser->text());
    settings_.setValue("from", ui->edtFrom->text());
    settings_.setValue("fromName", ui->edtFromName->text());
    settings_.setValue("to", ui->edtTo->text());
    settings_.setValue("isHtml", ui->chkHtml->isChecked());
}

void MainWindow::appendLog(const QString& text) {
    ui->txtLog->appendPlainText(text);
}

void MainWindow::on_btnSend_clicked() {
    // 基础校验
    if (ui->edtUser->text().isEmpty() || ui->edtAuth->text().isEmpty()) {
        appendLog("错误：用户名或授权码为空");
        return;
    }

    QStringList to;
    for (auto &s : ui->edtTo->text().split(',', Qt::SkipEmptyParts))
        to << s.trimmed();

    if (to.isEmpty()) {
        appendLog("错误：至少一个收件人");
        return;
    }

    // 组装配置
    SmtpConfig cfg;
    cfg.host = ui->edtHost->text();
    cfg.port = static_cast<quint16>(ui->spnPort->value());
    cfg.useSsl = ui->chkSSL->isChecked();
    cfg.username = ui->edtUser->text();
    cfg.authCode = ui->edtAuth->text();
    cfg.from = ui->edtFrom->text();
    cfg.fromName = ui->edtFromName->text();

    EmailSender sender(cfg);
    QString serverLog, err;
    bool ok = sender.send(to,
                          ui->edtSubject->text(),
                          ui->txtBody->toPlainText(),
                          ui->chkHtml->isChecked(),
                          &serverLog,
                          &err);
    appendLog(serverLog);
    if (ok)
        appendLog("发送成功");
    else
        appendLog("发送失败: " + err);
}

void MainWindow::on_btnClearLog_clicked() {
    ui->txtLog->clear();
}

void MainWindow::on_chkShowAuth_toggled(bool checked) {
    ui->edtAuth->setEchoMode(checked ? QLineEdit::Normal : QLineEdit::Password);
}
