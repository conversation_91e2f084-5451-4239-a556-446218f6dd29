#############################################################################
# Makefile for building: Emailtest
# Generated by qmake (3.1) (Qt 5.14.1)
# Project:  ..\Emailtest.pro
# Template: app
# Command: D:\Qt\Qt5.14.1\5.14.1\mingw73_64\bin\qmake.exe -o Makefile ..\Emailtest.pro -spec win32-g++ "CONFIG+=qtquickcompiler"
#############################################################################

MAKEFILE      = Makefile

EQ            = =

####### Compiler, tools and options

CC            = gcc
CXX           = g++
DEFINES       = -DUNICODE -D_UNICODE -DWIN32 -DMINGW_HAS_SECURE_API=1 -DQT_DEPRECATED_WARNINGS -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB -DQT_NEEDS_QMAIN
CFLAGS        = -fno-keep-inline-dllexport -O2 -Wall -Wextra -Wextra $(DEFINES)
CXXFLAGS      = -fno-keep-inline-dllexport -O2 -std=gnu++11 -Wall -Wextra -Wextra -fexceptions -mthreads $(DEFINES)
INCPATH       = -I..\..\Emailtest -I. -ID:\Qt\Qt5.14.1\5.14.1\mingw73_64\include -ID:\Qt\Qt5.14.1\5.14.1\mingw73_64\include\QtWidgets -ID:\Qt\Qt5.14.1\5.14.1\mingw73_64\include\QtGui -ID:\Qt\Qt5.14.1\5.14.1\mingw73_64\include\QtANGLE -ID:\Qt\Qt5.14.1\5.14.1\mingw73_64\include\QtNetwork -ID:\Qt\Qt5.14.1\5.14.1\mingw73_64\include\QtCore -I. -I. -I/include -ID:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\win32-g++ 
LINKER      =        g++
LFLAGS        =        -Wl,-s -Wl,-subsystem,windows -mthreads
LIBS        =        D:\Qt\Qt5.14.1\5.14.1\mingw73_64\lib\libQt5Widgets.a D:\Qt\Qt5.14.1\5.14.1\mingw73_64\lib\libQt5Gui.a D:\Qt\Qt5.14.1\5.14.1\mingw73_64\lib\libQt5Network.a D:\Qt\Qt5.14.1\5.14.1\mingw73_64\lib\libQt5Core.a  -lmingw32 D:\Qt\Qt5.14.1\5.14.1\mingw73_64\lib\libqtmain.a -LC:\openssl\lib -LC:\Utils\my_sql\mysql-5.7.25-winx64\lib -LC:\Utils\postgresql\pgsql\lib -lshell32 
QMAKE         = D:\Qt\Qt5.14.1\5.14.1\mingw73_64\bin\qmake.exe
DEL_FILE      = del
CHK_DIR_EXISTS= if not exist
MKDIR         = mkdir
COPY          = copy /y
COPY_FILE     = copy /y
COPY_DIR      = xcopy /s /q /y /i
INSTALL_FILE  = copy /y
INSTALL_PROGRAM = copy /y
INSTALL_DIR   = xcopy /s /q /y /i
QINSTALL      = D:\Qt\Qt5.14.1\5.14.1\mingw73_64\bin\qmake.exe -install qinstall
QINSTALL_PROGRAM = D:\Qt\Qt5.14.1\5.14.1\mingw73_64\bin\qmake.exe -install qinstall -exe
DEL_FILE      = del
SYMLINK       = $(QMAKE) -install ln -f -s
DEL_DIR       = rmdir
MOVE          = move
IDC           = idc
IDL           = midl
ZIP           = zip -r -9
DEF_FILE      = 
RES_FILE      = 
SED           = $(QMAKE) -install sed
MOVE          = move

####### Output directory

OBJECTS_DIR   = . 

####### Files

SOURCES       = ..\emailsender.cpp \
		..\main.cpp \
		..\mainwindow.cpp moc_emailsender.cpp \
		moc_mainwindow.cpp
OBJECTS       = emailsender.o \
		main.o \
		mainwindow.o \
		moc_emailsender.o \
		moc_mainwindow.o

DIST          =  ..\emailsender.h \
		..\mainwindow.h ..\emailsender.cpp \
		..\main.cpp \
		..\mainwindow.cpp
QMAKE_TARGET  = Emailtest
DESTDIR        =  #avoid trailing-slash linebreak
TARGET         = Emailtest.exe
DESTDIR_TARGET = Emailtest.exe

####### Build rules

first: all
all: Makefile  Emailtest.exe

Emailtest.exe: D:/Qt/Qt5.14.1/5.14.1/mingw73_64/lib/libQt5Widgets.a D:/Qt/Qt5.14.1/5.14.1/mingw73_64/lib/libQt5Gui.a D:/Qt/Qt5.14.1/5.14.1/mingw73_64/lib/libQt5Network.a D:/Qt/Qt5.14.1/5.14.1/mingw73_64/lib/libQt5Core.a D:/Qt/Qt5.14.1/5.14.1/mingw73_64/lib/libqtmain.a ui_mainwindow.h $(OBJECTS) 
	$(LINKER) $(LFLAGS) -o $(DESTDIR_TARGET) $(OBJECTS)  $(LIBS)

Makefile: ../Emailtest.pro D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/win32-g++/qmake.conf D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/spec_pre.prf \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/qdevice.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/device_config.prf \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/common/sanitize.conf \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/common/gcc-base.conf \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/common/g++-base.conf \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/common/angle.conf \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/win32/windows_vulkan_sdk.prf \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/common/windows-vulkan.conf \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/common/g++-win32.conf \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/common/windows-desktop.conf \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/qconfig.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3danimation.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3danimation_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3dcore.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3dcore_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3dextras.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3dextras_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3dinput.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3dinput_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3dlogic.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3dlogic_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3dquick.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3dquick_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3dquickanimation.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3dquickanimation_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3dquickextras.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3dquickextras_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3dquickinput.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3dquickinput_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3dquickrender.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3dquickrender_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3dquickscene2d.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3dquickscene2d_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3drender.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3drender_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_accessibility_support_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_axbase.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_axbase_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_axcontainer.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_axcontainer_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_axserver.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_axserver_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_bluetooth.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_bluetooth_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_bodymovin_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_bootstrap_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_charts.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_charts_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_concurrent.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_concurrent_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_core.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_core_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_datavisualization.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_datavisualization_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_dbus.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_dbus_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_designer.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_designer_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_designercomponents_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_devicediscovery_support_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_edid_support_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_egl_support_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_fb_support_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_fontdatabase_support_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_gamepad.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_gamepad_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_gui.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_gui_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_help.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_help_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_location.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_location_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_multimedia.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_multimedia_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_multimediawidgets.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_multimediawidgets_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_network.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_network_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_networkauth.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_networkauth_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_nfc.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_nfc_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_opengl.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_opengl_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_openglextensions.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_openglextensions_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_packetprotocol_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_platformcompositor_support_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_positioning.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_positioning_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_positioningquick.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_positioningquick_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_printsupport.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_printsupport_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_qml.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_qml_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_qmldebug_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_qmldevtools_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_qmlmodels.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_qmlmodels_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_qmltest.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_qmltest_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_qmlworkerscript.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_qmlworkerscript_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_qtmultimediaquicktools_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_quick.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_quick3d.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_quick3d_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_quick3dassetimport.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_quick3dassetimport_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_quick3drender.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_quick3drender_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_quick3druntimerender.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_quick3druntimerender_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_quick3dutils.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_quick3dutils_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_quick_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_quickcontrols2.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_quickcontrols2_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_quickparticles_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_quickshapes_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_quicktemplates2.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_quicktemplates2_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_quickwidgets.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_quickwidgets_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_remoteobjects.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_remoteobjects_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_repparser.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_repparser_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_script.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_script_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_scripttools.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_scripttools_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_scxml.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_scxml_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_sensors.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_sensors_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_serialbus.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_serialbus_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_serialport.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_serialport_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_sql.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_sql_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_svg.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_svg_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_testlib.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_testlib_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_texttospeech.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_texttospeech_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_theme_support_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_uiplugin.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_uitools.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_uitools_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_virtualkeyboard.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_virtualkeyboard_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_vulkan_support_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_webchannel.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_webchannel_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_websockets.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_websockets_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_widgets.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_widgets_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_windowsuiautomation_support_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_winextras.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_winextras_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_xml.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_xml_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_xmlpatterns.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_xmlpatterns_private.pri \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/qt_functions.prf \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/qt_config.prf \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/win32-g++/qmake.conf \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/spec_post.prf \
		.qmake.stash \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/exclusive_builds.prf \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/toolchain.prf \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/default_pre.prf \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/win32/default_pre.prf \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/resolve_config.prf \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/default_post.prf \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/resources_functions.prf \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/qtquickcompiler.prf \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/precompile_header.prf \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/warn_on.prf \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/qt.prf \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/resources.prf \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/moc.prf \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/win32/opengl.prf \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/uic.prf \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/qmake_use.prf \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/file_copies.prf \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/win32/windows.prf \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/testcase_targets.prf \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/exceptions.prf \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/yacc.prf \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/lex.prf \
		../Emailtest.pro \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/lib/Qt5Widgets.prl \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/lib/Qt5Gui.prl \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/lib/Qt5Network.prl \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/lib/Qt5Core.prl \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/lib/qtmain.prl
	$(QMAKE) -o Makefile ..\Emailtest.pro -spec win32-g++ "CONFIG+=qtquickcompiler"
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/spec_pre.prf:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/qdevice.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/device_config.prf:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/common/sanitize.conf:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/common/gcc-base.conf:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/common/g++-base.conf:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/common/angle.conf:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/win32/windows_vulkan_sdk.prf:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/common/windows-vulkan.conf:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/common/g++-win32.conf:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/common/windows-desktop.conf:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/qconfig.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3danimation.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3danimation_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3dcore.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3dcore_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3dextras.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3dextras_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3dinput.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3dinput_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3dlogic.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3dlogic_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3dquick.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3dquick_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3dquickanimation.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3dquickanimation_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3dquickextras.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3dquickextras_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3dquickinput.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3dquickinput_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3dquickrender.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3dquickrender_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3dquickscene2d.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3dquickscene2d_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3drender.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_3drender_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_accessibility_support_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_axbase.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_axbase_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_axcontainer.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_axcontainer_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_axserver.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_axserver_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_bluetooth.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_bluetooth_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_bodymovin_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_bootstrap_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_charts.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_charts_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_concurrent.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_concurrent_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_core.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_core_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_datavisualization.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_datavisualization_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_dbus.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_dbus_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_designer.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_designer_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_designercomponents_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_devicediscovery_support_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_edid_support_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_egl_support_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_fb_support_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_fontdatabase_support_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_gamepad.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_gamepad_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_gui.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_gui_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_help.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_help_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_location.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_location_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_multimedia.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_multimedia_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_multimediawidgets.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_multimediawidgets_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_network.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_network_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_networkauth.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_networkauth_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_nfc.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_nfc_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_opengl.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_opengl_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_openglextensions.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_openglextensions_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_packetprotocol_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_platformcompositor_support_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_positioning.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_positioning_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_positioningquick.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_positioningquick_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_printsupport.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_printsupport_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_qml.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_qml_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_qmldebug_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_qmldevtools_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_qmlmodels.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_qmlmodels_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_qmltest.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_qmltest_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_qmlworkerscript.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_qmlworkerscript_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_qtmultimediaquicktools_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_quick.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_quick3d.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_quick3d_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_quick3dassetimport.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_quick3dassetimport_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_quick3drender.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_quick3drender_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_quick3druntimerender.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_quick3druntimerender_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_quick3dutils.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_quick3dutils_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_quick_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_quickcontrols2.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_quickcontrols2_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_quickparticles_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_quickshapes_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_quicktemplates2.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_quicktemplates2_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_quickwidgets.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_quickwidgets_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_remoteobjects.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_remoteobjects_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_repparser.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_repparser_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_script.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_script_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_scripttools.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_scripttools_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_scxml.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_scxml_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_sensors.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_sensors_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_serialbus.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_serialbus_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_serialport.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_serialport_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_sql.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_sql_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_svg.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_svg_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_testlib.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_testlib_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_texttospeech.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_texttospeech_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_theme_support_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_uiplugin.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_uitools.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_uitools_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_virtualkeyboard.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_virtualkeyboard_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_vulkan_support_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_webchannel.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_webchannel_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_websockets.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_websockets_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_widgets.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_widgets_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_windowsuiautomation_support_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_winextras.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_winextras_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_xml.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_xml_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_xmlpatterns.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/modules/qt_lib_xmlpatterns_private.pri:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/qt_functions.prf:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/qt_config.prf:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/win32-g++/qmake.conf:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/spec_post.prf:
.qmake.stash:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/exclusive_builds.prf:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/toolchain.prf:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/default_pre.prf:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/win32/default_pre.prf:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/resolve_config.prf:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/default_post.prf:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/resources_functions.prf:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/qtquickcompiler.prf:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/precompile_header.prf:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/warn_on.prf:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/qt.prf:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/resources.prf:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/moc.prf:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/win32/opengl.prf:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/uic.prf:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/qmake_use.prf:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/file_copies.prf:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/win32/windows.prf:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/testcase_targets.prf:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/exceptions.prf:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/yacc.prf:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/lex.prf:
../Emailtest.pro:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/lib/Qt5Widgets.prl:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/lib/Qt5Gui.prl:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/lib/Qt5Network.prl:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/lib/Qt5Core.prl:
D:/Qt/Qt5.14.1/5.14.1/mingw73_64/lib/qtmain.prl:
qmake: FORCE
	@$(QMAKE) -o Makefile ..\Emailtest.pro -spec win32-g++ "CONFIG+=qtquickcompiler"

qmake_all: FORCE

dist:
	$(ZIP) Emailtest.zip $(SOURCES) $(DIST) ..\Emailtest.pro D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\features\spec_pre.prf D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\qdevice.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\features\device_config.prf D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\common\sanitize.conf D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\common\gcc-base.conf D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\common\g++-base.conf D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\common\angle.conf D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\features\win32\windows_vulkan_sdk.prf D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\common\windows-vulkan.conf D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\common\g++-win32.conf D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\common\windows-desktop.conf D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\qconfig.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_3danimation.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_3danimation_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_3dcore.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_3dcore_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_3dextras.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_3dextras_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_3dinput.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_3dinput_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_3dlogic.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_3dlogic_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_3dquick.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_3dquick_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_3dquickanimation.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_3dquickanimation_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_3dquickextras.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_3dquickextras_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_3dquickinput.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_3dquickinput_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_3dquickrender.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_3dquickrender_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_3dquickscene2d.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_3dquickscene2d_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_3drender.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_3drender_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_accessibility_support_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_axbase.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_axbase_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_axcontainer.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_axcontainer_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_axserver.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_axserver_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_bluetooth.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_bluetooth_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_bodymovin_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_bootstrap_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_charts.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_charts_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_concurrent.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_concurrent_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_core.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_core_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_datavisualization.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_datavisualization_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_dbus.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_dbus_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_designer.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_designer_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_designercomponents_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_devicediscovery_support_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_edid_support_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_egl_support_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_eventdispatcher_support_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_fb_support_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_fontdatabase_support_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_gamepad.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_gamepad_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_gui.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_gui_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_help.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_help_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_location.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_location_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_multimedia.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_multimedia_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_multimediawidgets.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_multimediawidgets_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_network.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_network_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_networkauth.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_networkauth_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_nfc.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_nfc_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_opengl.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_opengl_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_openglextensions.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_openglextensions_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_packetprotocol_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_platformcompositor_support_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_positioning.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_positioning_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_positioningquick.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_positioningquick_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_printsupport.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_printsupport_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_qml.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_qml_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_qmldebug_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_qmldevtools_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_qmlmodels.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_qmlmodels_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_qmltest.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_qmltest_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_qmlworkerscript.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_qmlworkerscript_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_qtmultimediaquicktools_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_quick.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_quick3d.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_quick3d_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_quick3dassetimport.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_quick3dassetimport_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_quick3drender.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_quick3drender_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_quick3druntimerender.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_quick3druntimerender_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_quick3dutils.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_quick3dutils_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_quick_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_quickcontrols2.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_quickcontrols2_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_quickparticles_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_quickshapes_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_quicktemplates2.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_quicktemplates2_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_quickwidgets.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_quickwidgets_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_remoteobjects.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_remoteobjects_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_repparser.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_repparser_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_script.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_script_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_scripttools.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_scripttools_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_scxml.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_scxml_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_sensors.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_sensors_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_serialbus.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_serialbus_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_serialport.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_serialport_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_sql.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_sql_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_svg.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_svg_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_testlib.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_testlib_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_texttospeech.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_texttospeech_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_theme_support_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_uiplugin.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_uitools.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_uitools_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_virtualkeyboard.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_virtualkeyboard_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_vulkan_support_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_webchannel.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_webchannel_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_websockets.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_websockets_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_widgets.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_widgets_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_windowsuiautomation_support_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_winextras.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_winextras_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_xml.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_xml_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_xmlpatterns.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\modules\qt_lib_xmlpatterns_private.pri D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\features\qt_functions.prf D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\features\qt_config.prf D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\win32-g++\qmake.conf D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\features\spec_post.prf .qmake.stash D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\features\exclusive_builds.prf D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\features\toolchain.prf D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\features\default_pre.prf D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\features\win32\default_pre.prf D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\features\resolve_config.prf D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\features\default_post.prf D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\features\resources_functions.prf D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\features\qtquickcompiler.prf D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\features\precompile_header.prf D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\features\warn_on.prf D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\features\qt.prf D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\features\resources.prf D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\features\moc.prf D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\features\win32\opengl.prf D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\features\uic.prf D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\features\qmake_use.prf D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\features\file_copies.prf D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\features\win32\windows.prf D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\features\testcase_targets.prf D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\features\exceptions.prf D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\features\yacc.prf D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\features\lex.prf ..\Emailtest.pro D:\Qt\Qt5.14.1\5.14.1\mingw73_64\lib\Qt5Widgets.prl D:\Qt\Qt5.14.1\5.14.1\mingw73_64\lib\Qt5Gui.prl D:\Qt\Qt5.14.1\5.14.1\mingw73_64\lib\Qt5Network.prl D:\Qt\Qt5.14.1\5.14.1\mingw73_64\lib\Qt5Core.prl D:\Qt\Qt5.14.1\5.14.1\mingw73_64\lib\qtmain.prl      D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\features\data\dummy.cpp ..\emailsender.h ..\mainwindow.h  ..\emailsender.cpp ..\main.cpp ..\mainwindow.cpp ..\mainwindow.ui    

clean: compiler_clean 
	-$(DEL_FILE) emailsender.o main.o mainwindow.o moc_emailsender.o moc_mainwindow.o

distclean: clean 
	-$(DEL_FILE) .qmake.stash
	-$(DEL_FILE) $(DESTDIR_TARGET)
	-$(DEL_FILE) Makefile

mocclean: compiler_moc_header_clean compiler_moc_objc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_objc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_qmlcache_make_all:
compiler_qmlcache_clean:
compiler_qmlcache_loader_make_all: qmlcache_loader.cpp
compiler_qmlcache_loader_clean:
	-$(DEL_FILE) qmlcache_loader.cpp
compiler_no_pch_compiler_make_all:
compiler_no_pch_compiler_clean:
compiler_rcc_make_all:
compiler_rcc_clean:
compiler_moc_predefs_make_all: moc_predefs.h
compiler_moc_predefs_clean:
	-$(DEL_FILE) moc_predefs.h
moc_predefs.h: D:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/features/data/dummy.cpp
	g++ -fno-keep-inline-dllexport -O2 -std=gnu++11 -Wall -Wextra -Wextra -dM -E -o moc_predefs.h D:\Qt\Qt5.14.1\5.14.1\mingw73_64\mkspecs\features\data\dummy.cpp

compiler_moc_header_make_all: moc_emailsender.cpp moc_mainwindow.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) moc_emailsender.cpp moc_mainwindow.cpp
moc_emailsender.cpp: ../emailsender.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/QSslSocket \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qsslsocket.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qtnetworkglobal.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qglobal.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qconfig-bootstrapped.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qconfig.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtcore-config.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsystemdetection.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qprocessordetection.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcompilerdetection.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtypeinfo.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsysinfo.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qlogging.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qflags.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qatomic.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qbasicatomic.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qatomic_bootstrap.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qgenericatomic.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qatomic_cxx11.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qatomic_msvc.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qglobalstatic.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmutex.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qnumeric.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qversiontagging.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qtnetwork-config.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qlist.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qalgorithms.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qiterator.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qrefcount.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qarraydata.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qhashfunctions.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstring.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qchar.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qbytearray.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qnamespace.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstringliteral.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstringalgorithms.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstringview.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstringbuilder.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qpair.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qvector.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcontainertools_impl.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qpoint.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qbytearraylist.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstringlist.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qregexp.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstringmatcher.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qtcpsocket.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qabstractsocket.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qiodevice.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qobject.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qobjectdefs.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qobjectdefs_impl.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcoreevent.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qscopedpointer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmetatype.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qvarlengtharray.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcontainerfwd.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qobject_impl.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qdebug.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qhash.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmap.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtextstream.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qlocale.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qvariant.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qshareddata.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qset.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcontiguouscache.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsharedpointer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsharedpointer_impl.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qsslerror.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qsslcertificate.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcryptographichash.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qdatetime.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qssl.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/QFlags \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/QSslCipher \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qsslcipher.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/QtCore \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/QtCoreDepends \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qabstractanimation.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qabstracteventdispatcher.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qeventloop.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qabstractitemmodel.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qabstractnativeeventfilter.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qabstractproxymodel.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qabstractstate.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qabstracttransition.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qanimationgroup.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qarraydataops.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qarraydatapointer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qbasictimer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qbitarray.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qbuffer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qbytearraymatcher.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcache.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcalendar.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcborarray.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcborvalue.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcborcommon.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qregularexpression.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qurl.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qurlquery.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/quuid.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcbormap.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcborstream.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfloat16.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcollator.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcommandlineoption.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcommandlineparser.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcoreapplication.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qconcatenatetablesproxymodel.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qdatastream.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qdeadlinetimer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qelapsedtimer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qdir.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfileinfo.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfile.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfiledevice.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qdiriterator.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qeasingcurve.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qendian.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qeventtransition.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qexception.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfactoryinterface.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfileselector.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/QObject \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/QStringList \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfilesystemwatcher.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfinalstate.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfuture.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfutureinterface.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qrunnable.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qresultstore.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfuturesynchronizer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfuturewatcher.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qhistorystate.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qidentityproxymodel.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qisenum.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qitemselectionmodel.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qjsonarray.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qjsonvalue.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qjsondocument.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qjsonobject.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qlibrary.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qlibraryinfo.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qversionnumber.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qline.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qlinkedlist.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qlockfile.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qloggingcategory.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmargins.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmath.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmessageauthenticationcode.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmetaobject.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmimedata.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmimedatabase.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmimetype.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qobjectcleanuphandler.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qoperatingsystemversion.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qparallelanimationgroup.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qpauseanimation.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qplugin.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qpointer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qpluginloader.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qprocess.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qpropertyanimation.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qvariantanimation.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qqueue.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qrandom.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qreadwritelock.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qrect.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsize.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qresource.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsavefile.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qscopedvaluerollback.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qscopeguard.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsemaphore.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsequentialanimationgroup.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsettings.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsharedmemory.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsignalmapper.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsignaltransition.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsocketnotifier.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsortfilterproxymodel.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstack.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstandardpaths.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstate.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstatemachine.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstorageinfo.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstringlistmodel.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsystemsemaphore.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtemporarydir.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtemporaryfile.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtextboundaryfinder.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtextcodec.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qthread.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qthreadpool.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qthreadstorage.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtimeline.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtimer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtimezone.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtranslator.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtransposeproxymodel.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtypetraits.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qwaitcondition.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qwineventnotifier.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qxmlstream.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtcoreversion.h \
		moc_predefs.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/bin/moc.exe
	D:\Qt\Qt5.14.1\5.14.1\mingw73_64\bin\moc.exe $(DEFINES) --include D:/All/StudyCode/QTcode/Emailtest/build-Emailtest-Desktop_Qt_5_14_1_MinGW_64_bit-Release/moc_predefs.h -ID:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/win32-g++ -ID:/All/StudyCode/QTcode/Emailtest -ID:/Qt/Qt5.14.1/5.14.1/mingw73_64/include -ID:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtWidgets -ID:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui -ID:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtANGLE -ID:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork -ID:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore -I. -ID:/Qt/Qt5.14.1/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include/c++ -ID:/Qt/Qt5.14.1/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include/c++/x86_64-w64-mingw32 -ID:/Qt/Qt5.14.1/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include/c++/backward -ID:/Qt/Qt5.14.1/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include -ID:/Qt/Qt5.14.1/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include-fixed -ID:/Qt/Qt5.14.1/Tools/mingw730_64/x86_64-w64-mingw32/include ..\emailsender.h -o moc_emailsender.cpp

moc_mainwindow.cpp: ../mainwindow.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtWidgets/QMainWindow \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtWidgets/qmainwindow.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qtguiglobal.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qglobal.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qconfig-bootstrapped.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qconfig.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtcore-config.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsystemdetection.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qprocessordetection.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcompilerdetection.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtypeinfo.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsysinfo.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qlogging.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qflags.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qatomic.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qbasicatomic.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qatomic_bootstrap.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qgenericatomic.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qatomic_cxx11.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qatomic_msvc.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qglobalstatic.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmutex.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qnumeric.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qversiontagging.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qtgui-config.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtWidgets/qtwidgets-config.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtWidgets/qwidget.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qwindowdefs.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qobjectdefs.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qnamespace.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qobjectdefs_impl.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qwindowdefs_win.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qobject.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstring.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qchar.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qbytearray.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qrefcount.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qarraydata.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstringliteral.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstringalgorithms.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstringview.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstringbuilder.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qlist.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qalgorithms.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qiterator.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qhashfunctions.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qpair.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qvector.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcontainertools_impl.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qpoint.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qbytearraylist.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstringlist.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qregexp.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstringmatcher.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcoreevent.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qscopedpointer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmetatype.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qvarlengtharray.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcontainerfwd.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qobject_impl.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmargins.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qpaintdevice.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qrect.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsize.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qpalette.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qcolor.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qrgb.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qrgba64.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qbrush.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qmatrix.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qpolygon.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qregion.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qdatastream.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qiodevice.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qline.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qtransform.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qpainterpath.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qimage.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qpixelformat.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qpixmap.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsharedpointer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qshareddata.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qhash.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsharedpointer_impl.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qfont.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qfontmetrics.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qfontinfo.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtWidgets/qsizepolicy.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qcursor.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qkeysequence.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qevent.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qvariant.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmap.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qdebug.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtextstream.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qlocale.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qset.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcontiguouscache.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qurl.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qurlquery.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfile.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfiledevice.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qvector2d.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qtouchdevice.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtWidgets/qtabwidget.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qicon.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtWidgets/QMessageBox \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtWidgets/qmessagebox.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtWidgets/qdialog.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/QThread \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qthread.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/QTimer \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtimer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qbasictimer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/QSettings \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsettings.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/QKeyEvent \
		../emailsender.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/QSslSocket \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qsslsocket.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qtnetworkglobal.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qtnetwork-config.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qtcpsocket.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qabstractsocket.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qsslerror.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qsslcertificate.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcryptographichash.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qdatetime.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qssl.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/QFlags \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/QSslCipher \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qsslcipher.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/QtCore \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/QtCoreDepends \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qabstractanimation.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qabstracteventdispatcher.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qeventloop.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qabstractitemmodel.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qabstractnativeeventfilter.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qabstractproxymodel.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qabstractstate.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qabstracttransition.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qanimationgroup.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qarraydataops.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qarraydatapointer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qbitarray.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qbuffer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qbytearraymatcher.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcache.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcalendar.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcborarray.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcborvalue.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcborcommon.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qregularexpression.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/quuid.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcbormap.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcborstream.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfloat16.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcollator.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcommandlineoption.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcommandlineparser.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcoreapplication.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qconcatenatetablesproxymodel.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qdeadlinetimer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qelapsedtimer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qdir.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfileinfo.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qdiriterator.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qeasingcurve.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qendian.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qeventtransition.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qexception.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfactoryinterface.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfileselector.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/QObject \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/QStringList \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfilesystemwatcher.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfinalstate.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfuture.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfutureinterface.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qrunnable.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qresultstore.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfuturesynchronizer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfuturewatcher.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qhistorystate.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qidentityproxymodel.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qisenum.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qitemselectionmodel.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qjsonarray.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qjsonvalue.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qjsondocument.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qjsonobject.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qlibrary.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qlibraryinfo.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qversionnumber.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qlinkedlist.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qlockfile.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qloggingcategory.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmath.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmessageauthenticationcode.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmetaobject.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmimedata.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmimedatabase.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmimetype.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qobjectcleanuphandler.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qoperatingsystemversion.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qparallelanimationgroup.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qpauseanimation.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qplugin.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qpointer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qpluginloader.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qprocess.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qpropertyanimation.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qvariantanimation.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qqueue.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qrandom.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qreadwritelock.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qresource.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsavefile.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qscopedvaluerollback.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qscopeguard.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsemaphore.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsequentialanimationgroup.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsharedmemory.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsignalmapper.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsignaltransition.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsocketnotifier.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsortfilterproxymodel.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstack.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstandardpaths.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstate.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstatemachine.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstorageinfo.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstringlistmodel.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsystemsemaphore.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtemporarydir.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtemporaryfile.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtextboundaryfinder.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtextcodec.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qthreadpool.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qthreadstorage.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtimeline.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtimezone.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtranslator.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtransposeproxymodel.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtypetraits.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qwaitcondition.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qwineventnotifier.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qxmlstream.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtcoreversion.h \
		moc_predefs.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/bin/moc.exe
	D:\Qt\Qt5.14.1\5.14.1\mingw73_64\bin\moc.exe $(DEFINES) --include D:/All/StudyCode/QTcode/Emailtest/build-Emailtest-Desktop_Qt_5_14_1_MinGW_64_bit-Release/moc_predefs.h -ID:/Qt/Qt5.14.1/5.14.1/mingw73_64/mkspecs/win32-g++ -ID:/All/StudyCode/QTcode/Emailtest -ID:/Qt/Qt5.14.1/5.14.1/mingw73_64/include -ID:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtWidgets -ID:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui -ID:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtANGLE -ID:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork -ID:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore -I. -ID:/Qt/Qt5.14.1/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include/c++ -ID:/Qt/Qt5.14.1/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include/c++/x86_64-w64-mingw32 -ID:/Qt/Qt5.14.1/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include/c++/backward -ID:/Qt/Qt5.14.1/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include -ID:/Qt/Qt5.14.1/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include-fixed -ID:/Qt/Qt5.14.1/Tools/mingw730_64/x86_64-w64-mingw32/include ..\mainwindow.h -o moc_mainwindow.cpp

compiler_moc_objc_header_make_all:
compiler_moc_objc_header_clean:
compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_uic_make_all: ui_mainwindow.h
compiler_uic_clean:
	-$(DEL_FILE) ui_mainwindow.h
ui_mainwindow.h: ../mainwindow.ui \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/bin/uic.exe
	D:\Qt\Qt5.14.1\5.14.1\mingw73_64\bin\uic.exe ..\mainwindow.ui -o ui_mainwindow.h

compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_moc_predefs_clean compiler_moc_header_clean compiler_uic_clean 



####### Compile

emailsender.o: ../emailsender.cpp ../emailsender.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/QSslSocket \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qsslsocket.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qtnetworkglobal.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qglobal.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qconfig-bootstrapped.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qconfig.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtcore-config.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsystemdetection.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qprocessordetection.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcompilerdetection.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtypeinfo.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsysinfo.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qlogging.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qflags.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qatomic.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qbasicatomic.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qatomic_bootstrap.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qgenericatomic.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qatomic_cxx11.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qatomic_msvc.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qglobalstatic.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmutex.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qnumeric.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qversiontagging.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qtnetwork-config.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qlist.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qalgorithms.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qiterator.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qrefcount.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qarraydata.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qhashfunctions.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstring.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qchar.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qbytearray.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qnamespace.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstringliteral.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstringalgorithms.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstringview.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstringbuilder.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qpair.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qvector.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcontainertools_impl.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qpoint.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qbytearraylist.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstringlist.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qregexp.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstringmatcher.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qtcpsocket.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qabstractsocket.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qiodevice.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qobject.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qobjectdefs.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qobjectdefs_impl.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcoreevent.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qscopedpointer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmetatype.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qvarlengtharray.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcontainerfwd.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qobject_impl.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qdebug.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qhash.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmap.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtextstream.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qlocale.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qvariant.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qshareddata.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qset.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcontiguouscache.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsharedpointer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsharedpointer_impl.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qsslerror.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qsslcertificate.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcryptographichash.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qdatetime.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qssl.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/QFlags \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/QSslCipher \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qsslcipher.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/QtCore \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/QtCoreDepends \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qabstractanimation.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qabstracteventdispatcher.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qeventloop.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qabstractitemmodel.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qabstractnativeeventfilter.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qabstractproxymodel.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qabstractstate.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qabstracttransition.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qanimationgroup.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qarraydataops.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qarraydatapointer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qbasictimer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qbitarray.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qbuffer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qbytearraymatcher.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcache.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcalendar.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcborarray.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcborvalue.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcborcommon.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qregularexpression.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qurl.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qurlquery.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/quuid.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcbormap.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcborstream.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfloat16.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcollator.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcommandlineoption.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcommandlineparser.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcoreapplication.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qconcatenatetablesproxymodel.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qdatastream.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qdeadlinetimer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qelapsedtimer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qdir.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfileinfo.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfile.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfiledevice.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qdiriterator.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qeasingcurve.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qendian.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qeventtransition.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qexception.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfactoryinterface.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfileselector.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/QObject \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/QStringList \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfilesystemwatcher.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfinalstate.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfuture.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfutureinterface.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qrunnable.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qresultstore.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfuturesynchronizer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfuturewatcher.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qhistorystate.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qidentityproxymodel.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qisenum.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qitemselectionmodel.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qjsonarray.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qjsonvalue.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qjsondocument.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qjsonobject.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qlibrary.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qlibraryinfo.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qversionnumber.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qline.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qlinkedlist.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qlockfile.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qloggingcategory.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmargins.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmath.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmessageauthenticationcode.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmetaobject.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmimedata.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmimedatabase.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmimetype.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qobjectcleanuphandler.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qoperatingsystemversion.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qparallelanimationgroup.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qpauseanimation.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qplugin.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qpointer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qpluginloader.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qprocess.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qpropertyanimation.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qvariantanimation.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qqueue.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qrandom.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qreadwritelock.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qrect.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsize.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qresource.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsavefile.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qscopedvaluerollback.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qscopeguard.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsemaphore.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsequentialanimationgroup.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsettings.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsharedmemory.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsignalmapper.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsignaltransition.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsocketnotifier.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsortfilterproxymodel.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstack.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstandardpaths.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstate.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstatemachine.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstorageinfo.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstringlistmodel.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsystemsemaphore.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtemporarydir.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtemporaryfile.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtextboundaryfinder.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtextcodec.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qthread.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qthreadpool.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qthreadstorage.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtimeline.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtimer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtimezone.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtranslator.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtransposeproxymodel.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtypetraits.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qwaitcondition.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qwineventnotifier.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qxmlstream.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtcoreversion.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o emailsender.o ..\emailsender.cpp

main.o: ../main.cpp D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtWidgets/QApplication \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtWidgets/qapplication.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qtguiglobal.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qglobal.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qconfig-bootstrapped.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qconfig.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtcore-config.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsystemdetection.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qprocessordetection.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcompilerdetection.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtypeinfo.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsysinfo.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qlogging.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qflags.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qatomic.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qbasicatomic.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qatomic_bootstrap.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qgenericatomic.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qatomic_cxx11.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qatomic_msvc.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qglobalstatic.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmutex.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qnumeric.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qversiontagging.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qtgui-config.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtWidgets/qtwidgets-config.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcoreapplication.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstring.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qchar.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qbytearray.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qrefcount.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qnamespace.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qarraydata.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstringliteral.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstringalgorithms.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstringview.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstringbuilder.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qobject.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qobjectdefs.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qobjectdefs_impl.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qlist.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qalgorithms.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qiterator.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qhashfunctions.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qpair.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qvector.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcontainertools_impl.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qpoint.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qbytearraylist.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstringlist.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qregexp.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstringmatcher.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcoreevent.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qscopedpointer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmetatype.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qvarlengtharray.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcontainerfwd.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qobject_impl.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qeventloop.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qwindowdefs.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qwindowdefs_win.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsize.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmargins.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qcursor.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtWidgets/qdesktopwidget.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtWidgets/qwidget.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qpaintdevice.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qrect.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qpalette.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qcolor.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qrgb.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qrgba64.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qbrush.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qmatrix.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qpolygon.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qregion.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qdatastream.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qiodevice.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qline.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qtransform.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qpainterpath.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qimage.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qpixelformat.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qpixmap.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsharedpointer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qshareddata.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qhash.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsharedpointer_impl.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qfont.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qfontmetrics.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qfontinfo.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtWidgets/qsizepolicy.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qkeysequence.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qevent.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qvariant.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmap.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qdebug.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtextstream.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qlocale.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qset.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcontiguouscache.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qurl.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qurlquery.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfile.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfiledevice.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qvector2d.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qtouchdevice.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qguiapplication.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qinputmethod.h \
		../mainwindow.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtWidgets/QMainWindow \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtWidgets/qmainwindow.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtWidgets/qtabwidget.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qicon.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtWidgets/QMessageBox \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtWidgets/qmessagebox.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtWidgets/qdialog.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/QThread \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qthread.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/QTimer \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtimer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qbasictimer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/QSettings \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsettings.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/QKeyEvent \
		../emailsender.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/QSslSocket \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qsslsocket.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qtnetworkglobal.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qtnetwork-config.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qtcpsocket.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qabstractsocket.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qsslerror.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qsslcertificate.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcryptographichash.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qdatetime.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qssl.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/QFlags \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/QSslCipher \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qsslcipher.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/QtCore \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/QtCoreDepends \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qabstractanimation.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qabstracteventdispatcher.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qabstractitemmodel.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qabstractnativeeventfilter.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qabstractproxymodel.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qabstractstate.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qabstracttransition.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qanimationgroup.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qarraydataops.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qarraydatapointer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qbitarray.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qbuffer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qbytearraymatcher.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcache.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcalendar.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcborarray.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcborvalue.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcborcommon.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qregularexpression.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/quuid.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcbormap.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcborstream.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfloat16.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcollator.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcommandlineoption.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcommandlineparser.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qconcatenatetablesproxymodel.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qdeadlinetimer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qelapsedtimer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qdir.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfileinfo.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qdiriterator.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qeasingcurve.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qendian.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qeventtransition.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qexception.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfactoryinterface.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfileselector.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/QObject \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/QStringList \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfilesystemwatcher.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfinalstate.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfuture.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfutureinterface.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qrunnable.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qresultstore.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfuturesynchronizer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfuturewatcher.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qhistorystate.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qidentityproxymodel.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qisenum.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qitemselectionmodel.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qjsonarray.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qjsonvalue.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qjsondocument.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qjsonobject.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qlibrary.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qlibraryinfo.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qversionnumber.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qlinkedlist.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qlockfile.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qloggingcategory.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmath.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmessageauthenticationcode.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmetaobject.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmimedata.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmimedatabase.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmimetype.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qobjectcleanuphandler.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qoperatingsystemversion.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qparallelanimationgroup.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qpauseanimation.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qplugin.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qpointer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qpluginloader.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qprocess.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qpropertyanimation.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qvariantanimation.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qqueue.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qrandom.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qreadwritelock.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qresource.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsavefile.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qscopedvaluerollback.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qscopeguard.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsemaphore.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsequentialanimationgroup.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsharedmemory.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsignalmapper.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsignaltransition.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsocketnotifier.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsortfilterproxymodel.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstack.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstandardpaths.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstate.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstatemachine.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstorageinfo.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstringlistmodel.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsystemsemaphore.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtemporarydir.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtemporaryfile.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtextboundaryfinder.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtextcodec.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qthreadpool.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qthreadstorage.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtimeline.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtimezone.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtranslator.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtransposeproxymodel.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtypetraits.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qwaitcondition.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qwineventnotifier.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qxmlstream.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtcoreversion.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o main.o ..\main.cpp

mainwindow.o: ../mainwindow.cpp ../mainwindow.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtWidgets/QMainWindow \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtWidgets/qmainwindow.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qtguiglobal.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qglobal.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qconfig-bootstrapped.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qconfig.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtcore-config.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsystemdetection.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qprocessordetection.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcompilerdetection.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtypeinfo.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsysinfo.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qlogging.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qflags.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qatomic.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qbasicatomic.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qatomic_bootstrap.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qgenericatomic.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qatomic_cxx11.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qatomic_msvc.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qglobalstatic.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmutex.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qnumeric.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qversiontagging.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qtgui-config.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtWidgets/qtwidgets-config.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtWidgets/qwidget.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qwindowdefs.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qobjectdefs.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qnamespace.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qobjectdefs_impl.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qwindowdefs_win.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qobject.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstring.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qchar.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qbytearray.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qrefcount.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qarraydata.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstringliteral.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstringalgorithms.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstringview.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstringbuilder.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qlist.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qalgorithms.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qiterator.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qhashfunctions.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qpair.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qvector.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcontainertools_impl.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qpoint.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qbytearraylist.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstringlist.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qregexp.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstringmatcher.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcoreevent.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qscopedpointer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmetatype.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qvarlengtharray.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcontainerfwd.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qobject_impl.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmargins.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qpaintdevice.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qrect.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsize.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qpalette.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qcolor.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qrgb.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qrgba64.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qbrush.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qmatrix.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qpolygon.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qregion.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qdatastream.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qiodevice.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qline.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qtransform.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qpainterpath.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qimage.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qpixelformat.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qpixmap.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsharedpointer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qshareddata.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qhash.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsharedpointer_impl.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qfont.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qfontmetrics.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qfontinfo.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtWidgets/qsizepolicy.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qcursor.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qkeysequence.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qevent.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qvariant.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmap.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qdebug.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtextstream.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qlocale.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qset.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcontiguouscache.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qurl.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qurlquery.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfile.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfiledevice.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qvector2d.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qtouchdevice.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtWidgets/qtabwidget.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/qicon.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtWidgets/QMessageBox \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtWidgets/qmessagebox.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtWidgets/qdialog.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/QThread \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qthread.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/QTimer \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtimer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qbasictimer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/QSettings \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsettings.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtGui/QKeyEvent \
		../emailsender.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/QSslSocket \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qsslsocket.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qtnetworkglobal.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qtnetwork-config.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qtcpsocket.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qabstractsocket.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qsslerror.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qsslcertificate.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcryptographichash.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qdatetime.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qssl.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/QFlags \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/QSslCipher \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtNetwork/qsslcipher.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/QtCore \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/QtCoreDepends \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qabstractanimation.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qabstracteventdispatcher.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qeventloop.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qabstractitemmodel.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qabstractnativeeventfilter.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qabstractproxymodel.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qabstractstate.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qabstracttransition.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qanimationgroup.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qarraydataops.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qarraydatapointer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qbitarray.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qbuffer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qbytearraymatcher.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcache.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcalendar.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcborarray.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcborvalue.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcborcommon.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qregularexpression.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/quuid.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcbormap.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcborstream.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfloat16.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcollator.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcommandlineoption.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcommandlineparser.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qcoreapplication.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qconcatenatetablesproxymodel.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qdeadlinetimer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qelapsedtimer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qdir.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfileinfo.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qdiriterator.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qeasingcurve.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qendian.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qeventtransition.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qexception.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfactoryinterface.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfileselector.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/QObject \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/QStringList \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfilesystemwatcher.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfinalstate.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfuture.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfutureinterface.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qrunnable.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qresultstore.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfuturesynchronizer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qfuturewatcher.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qhistorystate.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qidentityproxymodel.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qisenum.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qitemselectionmodel.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qjsonarray.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qjsonvalue.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qjsondocument.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qjsonobject.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qlibrary.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qlibraryinfo.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qversionnumber.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qlinkedlist.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qlockfile.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qloggingcategory.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmath.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmessageauthenticationcode.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmetaobject.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmimedata.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmimedatabase.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qmimetype.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qobjectcleanuphandler.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qoperatingsystemversion.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qparallelanimationgroup.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qpauseanimation.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qplugin.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qpointer.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qpluginloader.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qprocess.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qpropertyanimation.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qvariantanimation.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qqueue.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qrandom.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qreadwritelock.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qresource.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsavefile.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qscopedvaluerollback.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qscopeguard.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsemaphore.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsequentialanimationgroup.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsharedmemory.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsignalmapper.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsignaltransition.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsocketnotifier.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsortfilterproxymodel.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstack.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstandardpaths.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstate.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstatemachine.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstorageinfo.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qstringlistmodel.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qsystemsemaphore.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtemporarydir.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtemporaryfile.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtextboundaryfinder.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtextcodec.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qthreadpool.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qthreadstorage.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtimeline.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtimezone.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtranslator.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtransposeproxymodel.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtypetraits.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qwaitcondition.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qwineventnotifier.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qxmlstream.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/qtcoreversion.h \
		ui_mainwindow.h \
		D:/Qt/Qt5.14.1/5.14.1/mingw73_64/include/QtCore/QDateTime
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o mainwindow.o ..\mainwindow.cpp

moc_emailsender.o: moc_emailsender.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_emailsender.o moc_emailsender.cpp

moc_mainwindow.o: moc_mainwindow.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_mainwindow.o moc_mainwindow.cpp

####### Install

install:  FORCE

uninstall:  FORCE

FORCE:

